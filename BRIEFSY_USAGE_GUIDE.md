# Briefsy PAA Question Categorizer - Usage Guide

## Overview
This tool categorizes People Also Ask (PAA) questions for Briefsy blog content creation, focusing on bottom-of-the-funnel content related to newsletter curation, personalization, and content solutions. Uses VLLM with concurrent processing for fast results.

## Files Created
- `briefsy_paa_categorizer.py` - Main categorization script with concurrent processing
- `test_vllm_connection.py` - Test script to verify VLLM connection and concurrency
- `BRIEFSY_USAGE_GUIDE.md` - This guide

## Key Improvements Over Previous Version
- **10x Faster**: Concurrent processing (0.14 seconds vs 1-2 seconds per question)
- **VLLM Integration**: Uses your high-performance VLLM server
- **Concurrent Processing**: 10 simultaneous connections for maximum speed
- **Flexible Headers**: Handles both "Parent Category" and "parent_category" formats

## Setup

1. **Verify VLLM Connection**
   ```bash
   python test_vllm_connection.py
   ```
   This tests single connection, JSON parsing, and 10 concurrent connections.

## Usage

### Test Mode (Recommended First)
```bash
python briefsy_paa_categorizer.py --test
```
Processes first 20 questions with detailed timing information.

### Full Processing
```bash
python briefsy_paa_categorizer.py
```
Processes all questions using concurrent processing.

### Custom Files
```bash
python briefsy_paa_categorizer.py \
  --questions "your_questions.csv" \
  --categories "Briefsy Categories - Sheet1.csv" \
  --context "Briefsy_context.txt" \
  --output "your_output.csv"
```

## Input Files Expected

1. **PAA Questions CSV**: Column named "Questions" (same as before)
2. **Briefsy Categories CSV**: Columns "parent_category,child_category" (lowercase with underscores)
3. **Briefsy Context TXT**: Plain text file with Briefsy business context

## Output Format

Same 4-column format as requested:
- `original_question` - The original PAA question
- `parent_category` - Matched parent category (or empty if not relevant)
- `child_category` - Matched child category (or empty if not relevant)  
- `content_focus` - Paragraph explaining connection to Briefsy's business

## Performance Comparison

| Method | Time per Question | Total Time (21k questions) |
|--------|------------------|---------------------------|
| Sequential (LM Studio) | 1-2 seconds | ~10-12 hours |
| Concurrent (VLLM) | 0.14 seconds | ~50 minutes |

## Key Features

1. **Briefsy-Focused**: Prioritizes newsletter, curation, and personalization questions
2. **Concurrent Processing**: 10 simultaneous API calls for maximum speed
3. **Business Context Aware**: Uses Briefsy context for relevance filtering
4. **Flexible Category Headers**: Handles both naming conventions
5. **Progress Tracking**: Shows batch completion progress
6. **Error Handling**: Graceful handling of API errors and interruptions

## Expected Results for Briefsy

- Questions about newsletter curation, personalization, and content solutions will be categorized
- Generic AI questions will be marked as not relevant
- Focus on implementation and solution-oriented questions
- Each relevant question gets detailed content focus explaining blog potential

## Troubleshooting

1. **Connection Issues**: Run `test_vllm_connection.py` to diagnose
2. **Concurrent Failures**: The script will continue with successful requests
3. **File Format Issues**: Script handles both "Parent Category" and "parent_category" formats
4. **Slow Processing**: Should be ~0.14 seconds per question with concurrent processing

## Sample Results from Test

From the test run:
- **Processing Speed**: 20 questions in 2.8 seconds (0.14 seconds each)
- **Relevance Rate**: 30% relevant to Briefsy (6/20 questions)
- **Categories Used**: Newsletter & Content Curation, Information Overload Solutions, Personalization Strategies

## Next Steps

1. Run full processing on your PAA dataset
2. Filter for non-null categories to get relevant questions
3. Use content_focus paragraphs to guide blog post creation
4. Prioritize questions with detailed, specific content focus suggestions

The concurrent processing makes it practical to run the full 21k+ question dataset in under an hour!
