import React, { useState } from 'react';
import { Download, Shuffle, Settings } from 'lucide-react';

const BOFUTitleGenerator = () => {
  const [targetCount, setTargetCount] = useState(5000);
  const [generatedTitles, setGeneratedTitles] = useState([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [generateAll, setGenerateAll] = useState(false);
  const [mode, setMode] = useState('blog'); // 'blog' or 'google-ai'

  // Google AI Search optimized qualifiers (ultra-high authority)
  const googleAIQualifiers = [
    "Best", "Top", "Leading", "#1", "Top-Rated", "Premier"
  ];

  // High-authority qualifiers (optimized for blog content)
  const highAuthorityQualifiers = [
    // Top-tier authority keywords (Google AI loves these)
    "Best", "Top", "Leading", "#1", "Top-Rated", "Premier", "Expert",
    // Professional & trust signals
    "Professional", "Trusted", "Most Reliable", "Enterprise",
    // Action-oriented (high commercial intent)
    "Hire", "Find", "Custom"
  ];

  // All qualifiers (for Generate All mode)
  const allQualifiers = [
    ...highAuthorityQualifiers,
    // Additional qualifiers (only used in Generate All mode)
    "Fastest", "Affordable", "Fix for", "Solve", "Alternative to",
    "Replacement for", "Upgrade from", "Cost of", "Pricing", "ROI of",
    "Value of", "Review", "Guide to Choosing", "Comparison"
  ];

  // Enhanced service types with AI Agent focus and problem/solution language
  const serviceTypes = [
    // Original
    "AI Development Company", "AI Agency", "AI Automation Agency",
    "Custom AI Solutions", "AI Workflow Automation", "AI Integration Services",
    "Multi-Agent Systems", "Custom Software Development", "Business Automation Solutions",
    "SaaS Development Company",

    // AI Agent-specific
    "AI Agent Development", "Custom AI Agent Builders", "Autonomous AI Agent Solutions",
    "AI Sales Agent Development", "AI Customer Support Agents", "AI Research Agents",

    // Problem/solution focused
    "SaaS Subscription Consolidation", "Workflow Integration Solutions",
    "Business Process Automation", "Custom Internal Tool Development",

    // Strategic & niche services
    "AI SEO Systems", "LLM Ranking Solutions", "AI Influencer Creation",
    "Generative AI Integration", "AI-Powered Business Intelligence",
    "AI Chatbot Development", "Voice AI Solutions", "AI-Powered Software",
    "AI Content Generation", "Lead Generation AI", "AI CRM Integration",
    "AI Marketing Automation", "AI Sales Tools", "Intelligent Workflow Systems",
    "AI Dashboard Development", "RAG System Development", "LangGraph Developers"
  ];

  // Google AI Search optimized industries (most cited sectors)
  const googleAIIndustries = [
    // Most frequently cited in AI search results
    "SaaS Companies", "Fintech", "Healthcare", "Legal Services", "Real Estate",
    "E-commerce", "Manufacturing", "Financial Services", "Technology Companies",
    "Marketing Agencies", "Consulting Firms", "Investment Firms"
  ];

  // High-value industries (curated for 20K optimization)
  const highValueIndustries = [
    // High-Revenue Professional Services
    "Law Firms", "Legal Services", "Accounting Firms", "Management Consulting",
    "Financial Advisors", "Insurance Agencies", "Architecture Firms", "Engineering Firms",

    // Healthcare (High-Value)
    "Medical Practices", "Dental Clinics", "Veterinary Clinics", "Mental Health",
    "Physical Therapy", "Pharmacies", "Home Healthcare",

    // Finance & Investment
    "Banks", "Credit Unions", "Fintech", "Investment Firms", "Wealth Management",
    "Private Equity Firms", "Venture Capital", "Lending Companies",

    // Real Estate (High-Transaction)
    "Real Estate Agencies", "Commercial Real Estate", "Property Management",
    "Mortgage Brokers", "Real Estate Investment",

    // Digital-First & Tech (High-Growth)
    "SaaS Companies", "Digital Marketing Agencies", "Software Development Houses",
    "Tech Startups", "E-learning Platforms", "Fintech",

    // High-Volume Service Industries
    "Hotels", "Restaurants", "Construction", "Home Builders", "HVAC",
    "Plumbing", "Electrical", "Marketing Agencies", "PR Firms",

    // E-commerce & Retail (Scalable)
    "E-commerce Stores", "Online Retailers", "Manufacturing", "Wholesale",
    "Distribution", "Logistics", "Supply Chain",

    // Education & Training (Scalable)
    "Universities", "Online Education", "Training Centers", "Recruitment", "HR Services",

    // Automotive (High-Value)
    "Auto Dealers", "Car Dealerships", "Auto Repair",

    // Operations-Heavy (Franchise Potential)
    "Franchise Businesses"
  ];

  // All industries (for Generate All mode)
  const allIndustries = [
    ...highValueIndustries,
    // Additional niche industries (only used in Generate All mode)
    "Landscaping", "Snow Removal", "Roofing", "Cleaning Services",
    "Pest Control", "Pool Maintenance", "Tree Services", "Locksmith",
    "Cafes", "Bars", "Catering", "Food Delivery", "Event Planning",
    "Wedding Planning", "Travel Agencies", "Event Management Companies",
    "Tax Preparation", "Chiropractic", "Medical Spas", "Fitness Centers",
    "Yoga Studios", "Title Companies", "Auto Body Shops", "Car Washes",
    "Auto Parts", "RV Dealers", "Motorcycle Dealers", "Retail Shops",
    "Fashion Boutiques", "Jewelry Stores", "Furniture Stores", "Home Decor",
    "Electronics Stores", "Schools", "Tutoring Services", "Language Schools",
    "Daycare Centers", "Payment Processing", "Warehousing", "Industrial Services",
    "Media & Publishing", "App Developers", "Non-Profits"
  ];

  // Enhanced specific products with better naming
  const specificProducts = {
    "Lead Generation": [
      "AI Lead Generation", "Autonomous Lead Qualification Agents",
      "Lead Scoring AI", "Lead Nurturing Automation",
      "AI Sales Development Rep (SDR) Automation", "Inbound Lead Routing Systems"
    ],
    "Customer Service": [
      "AI Chatbots", "Voice AI Agents", "Support Ticket Automation",
      "24/7 AI Support", "AI Customer Support Agents"
    ],
    "Marketing": [
      "Content Automation", "Social Media AI", "Email Marketing AI",
      "SEO Automation", "AI Content Supply Chain", "Personalized Content Engines",
      "Automated Brand Voice Systems", "AI SEO Content Clusters"
    ],
    "Sales": [
      "Sales Automation", "CRM Integration", "Proposal Generation AI",
      "Follow-up Automation", "AI Sales Agents"
    ],
    "Operations": [
      "Workflow Automation", "Document Processing AI", "Scheduling Automation",
      "Inventory Management AI", "Custom Operational Dashboards",
      "Subscription Chaos Consolidation", "Custom Internal Software", "API Integration Hubs"
    ],
    "Analytics": [
      "AI Dashboards", "Predictive Analytics", "Business Intelligence AI",
      "Reporting Automation"
    ],
    "Intelligence & Research": [
      "Competitive Analysis Agents", "Market Trend Monitoring Systems",
      "Automated Due Diligence AI", "Social Media Intelligence Tools"
    ]
  };

  // No-code platforms for comparison titles
  const noCodePlatforms = ["Zapier", "Make.com", "n8n", "ChatGPT Plus"];

  // Google AI Search optimized templates (ultra-focused)
  const googleAITemplates = [
    // Most cited patterns in Google AI search results - each creates unique content
    "{qualifier} {service} for {industry} in 2025",
    "{qualifier} {product} for {industry}",
    "{industry}: {qualifier} {service}",
    "{qualifier} {service} Companies for {industry}",
    "Hire {qualifier} {service} for Your {industry} Business"
  ];

  // High-authority title templates (optimized for blog content)
  const highAuthorityTemplates = [
    // Core authority templates - each creates unique content angles
    "{qualifier} {service} for {industry} in 2025",
    "{qualifier} {product} for {industry} Businesses",
    "{industry}: {qualifier} {service} Comparison",
    "{qualifier} {service} Specialized for {industry}",
    "{product} for {industry}: {qualifier} Solutions",
    "{qualifier} Companies Providing {service} for {industry}",
    "Hire {qualifier} {service} for Your {industry} Business",
    "{industry} AI Solutions: {qualifier} {service}",
    "{qualifier} Custom AI for {industry}",

    // Platform comparison (high commercial intent) - unique competitive angles
    "{service} vs. {platform}: A Guide for {industry}",
    "Custom AI Agents vs. {platform} for {industry}",
    "{qualifier} {platform} Alternative for {industry}",
    "Why {industry} Companies Are Moving from {platform} to Custom AI",
    "Beyond {platform}: {qualifier} {service} for {industry}",

    // Business transformation (high-value) - unique transformation focus
    "Transform Your {industry} Business with {service}",
    "{industry} Digital Transformation: {qualifier} {service}",
    "{industry} Automation: {qualifier} {service}"
  ];

  // All templates (for Generate All mode)
  const allTemplates = [
    ...highAuthorityTemplates,
    // Additional templates (only used in Generate All mode)
    "Why {industry} Companies Need {service}",
    "Tired of Broken Workflows? How {service} Can Help Your {industry} Business",
    "Is {platform} Holding Your {industry} Business Back?",
    "5 Signs You've Outgrown {platform} in Your {industry} Business",
    "How to Build a {product} System for Your {industry} Company",
    "The Ultimate Guide to {service} for {industry}",
    "Building vs. Buying: {service} for {industry}",
    "{industry} Guide: When to Move Beyond {platform}",
    "The True Cost of {platform} for {industry} Companies",
    "ROI Analysis: Custom {service} vs. {platform} for {industry}",
    "Cost Breakdown: {service} for {industry} Businesses"
  ];

  const generateTitles = () => {
    setIsGenerating(true);
    const titles = new Set();

    // Choose optimized lists based on mode and generateAll setting
    let selectedQualifiers, selectedIndustries, selectedTemplates;

    if (mode === 'google-ai') {
      // Google AI Search mode - ultra-focused
      selectedQualifiers = googleAIQualifiers;
      selectedIndustries = googleAIIndustries;
      selectedTemplates = googleAITemplates;
    } else if (generateAll) {
      // Blog mode with all options
      selectedQualifiers = allQualifiers;
      selectedIndustries = allIndustries;
      selectedTemplates = allTemplates;
    } else {
      // Blog mode optimized (~20K)
      selectedQualifiers = highAuthorityQualifiers;
      selectedIndustries = highValueIndustries;
      selectedTemplates = highAuthorityTemplates;
    }

    // Strategy 1: Core Service + Industry combinations
    serviceTypes.forEach(service => {
      selectedIndustries.forEach(industry => {
        const qualifierLimit = generateAll ? selectedQualifiers.length : Math.min(6, selectedQualifiers.length);
        const templateLimit = generateAll ? selectedTemplates.filter(t => !t.includes('{platform}')).length : 8;

        selectedQualifiers.slice(0, qualifierLimit).forEach(qualifier => {
          selectedTemplates.filter(t => !t.includes('{platform}')).slice(0, templateLimit).forEach(template => {
            const title = template
              .replace('{qualifier}', qualifier)
              .replace('{service}', service)
              .replace('{product}', service) // Treat service as product when needed
              .replace('{industry}', industry);
            titles.add(title);
          });
        });
      });
    });

    // Strategy 2: Specific Products + Industries (optimized)
    Object.entries(specificProducts).forEach(([, products]) => {
      products.forEach(product => {
        selectedIndustries.forEach(industry => {
          const qualifierLimit = generateAll ? selectedQualifiers.length : 4;
          selectedQualifiers.slice(0, qualifierLimit).forEach(qualifier => {
            // Prioritize templates that use {product}, then fall back to {service} templates
            const productTemplates = selectedTemplates.filter(t => t.includes('{product}') && !t.includes('{platform}'));
            const serviceTemplates = selectedTemplates.filter(t => t.includes('{service}') && !t.includes('{platform}') && !t.includes('{product}'));

            // Use product templates first
            productTemplates.forEach(template => {
              const title = template
                .replace('{qualifier}', qualifier)
                .replace('{service}', product)
                .replace('{product}', product)
                .replace('{industry}', industry);
              titles.add(title);
            });

            // Also use some service templates (treating product as service)
            if (!generateAll) {
              // In limited mode, use fewer service templates
              serviceTemplates.slice(0, 1).forEach(template => {
                const title = template
                  .replace('{qualifier}', qualifier)
                  .replace('{service}', product)
                  .replace('{product}', product)
                  .replace('{industry}', industry);
                titles.add(title);
              });
            } else {
              // In generate all mode, use all service templates
              serviceTemplates.forEach(template => {
                const title = template
                  .replace('{qualifier}', qualifier)
                  .replace('{service}', product)
                  .replace('{product}', product)
                  .replace('{industry}', industry);
                titles.add(title);
              });
            }
          });
        });
      });
    });

    // Strategy 3: Platform Comparison Titles (HIGH VALUE!)
    noCodePlatforms.forEach(platform => {
      selectedIndustries.forEach(industry => {
        const serviceLimit = generateAll ? serviceTypes.length : 10;
        serviceTypes.slice(0, serviceLimit).forEach(service => {
          selectedTemplates.filter(t => t.includes('{platform}')).forEach(template => {
            const title = template
              .replace('{platform}', platform)
              .replace('{service}', service)
              .replace('{product}', service) // Treat service as product when needed
              .replace('{industry}', industry)
              .replace('{qualifier}', selectedQualifiers[Math.floor(Math.random() * Math.min(3, selectedQualifiers.length))]);
            titles.add(title);
          });
        });
      });
    });

    // Strategy 4: High-intent problem/solution variations (reduced for 20K target)
    if (generateAll) {
      const painPoints = [
        "Subscription Chaos", "Broken Integrations", "Manual Data Entry",
        "Scaling Issues", "High Per-Seat Costs", "Workflow Bottlenecks"
      ];

      painPoints.forEach(pain => {
        selectedIndustries.forEach(industry => {
          titles.add(`How to Solve ${pain} in ${industry} with Custom AI`);
          titles.add(`${industry} Companies: Stop Paying for ${pain}`);
        });
      });
    }

    // Strategy 5: Product-specific comparison titles (optimized)
    Object.entries(specificProducts).forEach(([, products]) => {
      const productLimit = generateAll ? products.length : 2;
      products.slice(0, productLimit).forEach(product => {
        noCodePlatforms.forEach(platform => {
          const industryLimit = generateAll ? selectedIndustries.length : 15;
          selectedIndustries.slice(0, industryLimit).forEach(industry => {
            titles.add(`${product} vs. ${platform} for ${industry}`);
            if (generateAll) {
              titles.add(`Building Custom ${product} for ${industry}: Beyond ${platform}`);
            }
          });
        });
      });
    });

    // Convert to array and limit to target count if not generating all
    const titlesArray = generateAll ? Array.from(titles) : Array.from(titles).slice(0, targetCount);

    // Add metadata
    const titlesWithMeta = titlesArray.map((title, index) => ({
      id: index + 1,
      title: title,
      category: getCategoryFromTitle(title),
      intent: 'BOFU',
      hasComparison: title.includes('vs.') || noCodePlatforms.some(p => title.includes(p)),
      estimatedSearchVolume: Math.floor(Math.random() * 500) + 50
    }));

    setGeneratedTitles(titlesWithMeta);
    setIsGenerating(false);
  };

  const getCategoryFromTitle = (title) => {
    if (title.includes('Lead') || title.includes('SDR')) return 'Lead Generation';
    if (title.includes('Customer Service') || title.includes('Chatbot') || title.includes('Voice AI') || title.includes('Support')) return 'Customer Service';
    if (title.includes('Marketing') || title.includes('Content') || title.includes('Social') || title.includes('SEO')) return 'Marketing';
    if (title.includes('Sales') || title.includes('CRM')) return 'Sales';
    if (title.includes('Workflow') || title.includes('Automation') || title.includes('Operations') || title.includes('Dashboard')) return 'Operations';
    if (title.includes('Intelligence') || title.includes('Research') || title.includes('Analysis')) return 'Intelligence & Research';
    if (title.includes('vs.') || title.includes('Alternative') || title.includes('Outgrown') || noCodePlatforms.some(p => title.includes(p))) return 'Platform Comparison';
    return 'General AI Development';
  };

  const downloadCSV = () => {
    const headers = ['ID', 'Title', 'Category', 'Intent', 'Has Comparison', 'Estimated Volume'];
    const csvContent = [
      headers.join(','),
      ...generatedTitles.map(item =>
        `${item.id},"${item.title}",${item.category},${item.intent},${item.hasComparison},${item.estimatedSearchVolume}`
      )
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `aiq-bofu-titles-${generatedTitles.length}-${Date.now()}.csv`;
    a.click();
  };

  const downloadJSON = () => {
    const json = JSON.stringify(generatedTitles, null, 2);
    const blob = new Blob([json], { type: 'application/json' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `aiq-bofu-titles-${generatedTitles.length}-${Date.now()}.json`;
    a.click();
  };

  const getCategoryColor = (category) => {
    const colors = {
      'Lead Generation': { bg: 'rgba(34, 197, 94, 0.2)', text: '#86efac' },
      'Customer Service': { bg: 'rgba(59, 130, 246, 0.2)', text: '#93c5fd' },
      'Marketing': { bg: 'rgba(139, 92, 246, 0.2)', text: '#c4b5fd' },
      'Sales': { bg: 'rgba(236, 72, 153, 0.2)', text: '#f9a8d4' },
      'Operations': { bg: 'rgba(249, 115, 22, 0.2)', text: '#fdba74' },
      'Intelligence & Research': { bg: 'rgba(6, 182, 212, 0.2)', text: '#67e8f9' },
      'Platform Comparison': { bg: 'rgba(239, 68, 68, 0.2)', text: '#fca5a5' },
      'General AI Development': { bg: 'rgba(107, 114, 128, 0.2)', text: '#d1d5db' }
    };
    return colors[category] || { bg: 'rgba(255, 255, 255, 0.1)', text: 'white' };
  };

  return (
    <div style={{ minHeight: '100vh', padding: '2rem' }}>
      <div className="container">
        {/* Header */}
        <div className="card">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold text-white mb-2">AIQ Labs BOFU Title Generator</h1>
              <p className="text-purple-200">Generate thousands of high-intent article titles targeting businesses ready to hire custom AI developers</p>
            </div>
            <div style={{ textAlign: 'right' }}>
              <div className="text-3xl font-bold text-white">
                {generateAll ? 'ALL' : targetCount.toLocaleString()}
              </div>
              <div className="text-purple-300 text-sm">Target Titles</div>
            </div>
          </div>
        </div>

        {/* Mode Selector */}
        <div className="card">
          <div className="mb-6">
            <h3 className="text-white font-semibold mb-3">Choose Generation Mode</h3>
            <div className="flex gap-4">
              <button
                onClick={() => setMode('blog')}
                className={`btn ${mode === 'blog' ? 'btn-primary' : 'btn-secondary'}`}
              >
                📝 Blog Content Mode (~20K titles)
              </button>
              <button
                onClick={() => setMode('google-ai')}
                className={`btn ${mode === 'google-ai' ? 'btn-primary' : 'btn-secondary'}`}
              >
                🤖 Google AI Search Mode (~2K titles)
              </button>
            </div>
            <div className="mt-3 text-sm text-purple-200">
              {mode === 'blog' ?
                "Optimized for AI research and blog post creation with high-value industries and authority keywords." :
                "Ultra-focused titles optimized for Google AI search mentions using only the most cited patterns."
              }
            </div>
          </div>
        </div>

        {/* Controls */}
        <div className="card">
          <div className="flex items-center gap-4 flex-wrap">
            {mode === 'blog' && (
              <div className="flex-1 min-w-64">
                <label className="text-purple-200 mb-2 text-sm font-semibold" style={{ display: 'block' }}>
                  Target Title Count
                </label>
                <input
                  type="number"
                  value={targetCount}
                  onChange={(e) => setTargetCount(Math.min(50000, Math.max(100, parseInt(e.target.value) || 1000)))}
                  className="input"
                  min="100"
                  max="50000"
                  step="100"
                  disabled={generateAll}
                />
              </div>
            )}

            {mode === 'blog' && (
              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="generateAll"
                  checked={generateAll}
                  onChange={(e) => setGenerateAll(e.target.checked)}
                  style={{ width: '1rem', height: '1rem' }}
                />
                <label htmlFor="generateAll" className="text-purple-200 text-sm font-semibold">
                  Generate All Possible Combinations
                </label>
              </div>
            )}

            <button
              onClick={generateTitles}
              disabled={isGenerating}
              className="btn btn-primary disabled:opacity-50"
            >
              <Shuffle size={20} />
              {isGenerating ? 'Generating...' :
                mode === 'google-ai' ? 'Generate Google AI Titles' :
                generateAll ? 'Generate All Titles' : 'Generate Titles'
              }
            </button>

            <button
              onClick={() => setShowSettings(!showSettings)}
              className="btn btn-secondary"
            >
              <Settings size={20} />
            </button>
          </div>

          {generateAll && mode === 'blog' && (
            <div className="mt-4 p-4" style={{ background: 'rgba(234, 179, 8, 0.2)', border: '1px solid rgba(234, 179, 8, 0.3)', borderRadius: '0.5rem' }}>
              <p className="text-yellow-300 text-sm">
                <strong>⚠️ Generate All Mode:</strong> This will create all possible title combinations.
                This could generate 2M+ titles and may take several minutes to complete.
              </p>
            </div>
          )}

          {mode === 'google-ai' && (
            <div className="mt-4 p-4" style={{ background: 'rgba(34, 197, 94, 0.2)', border: '1px solid rgba(34, 197, 94, 0.3)', borderRadius: '0.5rem' }}>
              <p className="text-green-300 text-sm">
                <strong>🤖 Google AI Mode:</strong> Ultra-focused on the most cited title patterns in Google AI search results.
                Perfect for maximum authority and search visibility.
              </p>
            </div>
          )}

          {showSettings && (
            <div className="mt-6 pt-6 border-t">
              <h3 className="text-white font-semibold mb-4">Generator Configuration</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-6">
                <div style={{ background: 'rgba(255, 255, 255, 0.1)', borderRadius: '0.5rem' }} className="p-3">
                  <div className="text-purple-300">Qualifiers</div>
                  <div className="text-2xl font-bold text-white">
                    {mode === 'google-ai' ? googleAIQualifiers.length :
                     generateAll ? allQualifiers.length : highAuthorityQualifiers.length}
                  </div>
                </div>
                <div style={{ background: 'rgba(255, 255, 255, 0.1)', borderRadius: '0.5rem' }} className="p-3">
                  <div className="text-purple-300">Services</div>
                  <div className="text-2xl font-bold text-white">{serviceTypes.length}</div>
                </div>
                <div style={{ background: 'rgba(255, 255, 255, 0.1)', borderRadius: '0.5rem' }} className="p-3">
                  <div className="text-purple-300">Industries</div>
                  <div className="text-2xl font-bold text-white">
                    {mode === 'google-ai' ? googleAIIndustries.length :
                     generateAll ? allIndustries.length : highValueIndustries.length}
                  </div>
                </div>
                <div style={{ background: 'rgba(255, 255, 255, 0.1)', borderRadius: '0.5rem' }} className="p-3">
                  <div className="text-purple-300">Templates</div>
                  <div className="text-2xl font-bold text-white">
                    {mode === 'google-ai' ? googleAITemplates.length :
                     generateAll ? allTemplates.length : highAuthorityTemplates.length}
                  </div>
                </div>
              </div>

              <div className="p-4 rounded-lg" style={{ background: 'rgba(139, 92, 246, 0.2)', border: '1px solid rgba(139, 92, 246, 0.3)' }}>
                <h4 className="text-purple-200 font-semibold mb-2">
                  {mode === 'google-ai' ? '🤖 Google AI Features:' : '🎯 Blog Content Features:'}
                </h4>
                <ul className="text-purple-200 text-sm space-y-1 list-disc list-inside">
                  {mode === 'google-ai' ? (
                    <>
                      <li><strong>Ultra-focused (~2K titles):</strong> Only the most cited patterns</li>
                      <li><strong>Top 6 qualifiers:</strong> "Best", "Top", "Leading", "#1", "Top-Rated", "Premier"</li>
                      <li><strong>12 premium industries:</strong> Most frequently mentioned in AI search</li>
                      <li><strong>5 core templates:</strong> Highest authority patterns only</li>
                      <li>Optimized for Google AI search mentions and citations</li>
                      <li>Maximum commercial intent and professional credibility</li>
                    </>
                  ) : (
                    <>
                      <li><strong>Smart Mode (~20K titles):</strong> High-authority qualifiers + premium industries</li>
                      <li><strong>Generate All Mode (2M+ titles):</strong> Complete coverage including niche markets</li>
                      <li>Platform comparison titles (vs. Zapier, Make.com, n8n, ChatGPT Plus)</li>
                      <li>Google AI search optimized with "Best", "Top", "Leading" keywords</li>
                      <li>Removed "How-To" templates for higher commercial intent</li>
                      <li>AI Agent-specific service positioning across all industries</li>
                      <li>Focus on high-value industries: Finance, Healthcare, Legal, Tech, Real Estate</li>
                    </>
                  )}
                </ul>
              </div>
            </div>
          )}
        </div>

        {/* Results */}
        {generatedTitles.length > 0 && (
          <>
            <div className="card">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h2 className="text-2xl font-bold text-white">Generated Titles</h2>
                  <p className="text-purple-200">
                    {generatedTitles.length.toLocaleString()} titles ready | {generatedTitles.filter(t => t.hasComparison).length} include platform comparisons
                    {generateAll && <span style={{ marginLeft: '0.5rem' }} className="text-yellow-300">• All possible combinations generated</span>}
                  </p>
                </div>
                <div className="flex gap-3">
                  <button
                    onClick={downloadCSV}
                    className="btn bg-green-500 hover:bg-green-600"
                    style={{ color: 'white' }}
                  >
                    <Download size={18} />
                    Export CSV
                  </button>
                  <button
                    onClick={downloadJSON}
                    className="btn bg-blue-500 hover:bg-blue-600"
                    style={{ color: 'white' }}
                  >
                    <Download size={18} />
                    Export JSON
                  </button>
                </div>
              </div>

              {/* Category breakdown */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-6">
                {[...new Set(generatedTitles.map(t => t.category))].sort().map(category => (
                  <div key={category} className="rounded-lg p-3" style={{
                    background: getCategoryColor(category).bg,
                    color: getCategoryColor(category).text
                  }}>
                    <div className="text-xs mb-1 opacity-80">{category}</div>
                    <div className="font-bold text-lg">
                      {generatedTitles.filter(t => t.category === category).length}
                    </div>
                  </div>
                ))}
              </div>

              {/* Sample titles */}
              <div className="rounded-lg p-4 max-h-96 overflow-y-auto" style={{ background: 'rgba(255, 255, 255, 0.05)' }}>
                <h3 className="text-white font-semibold mb-3">Sample Titles (first 50)</h3>
                <div className="space-y-2">
                  {generatedTitles.slice(0, 50).map((item) => (
                    <div key={item.id} className="flex items-start gap-3 text-sm p-3 rounded hover:bg-white/10 transition-colors" style={{ background: 'rgba(255, 255, 255, 0.05)' }}>
                      <span className="text-purple-300 text-xs" style={{ fontFamily: 'monospace' }}>{item.id}</span>
                      <span className="text-white flex-1">{item.title}</span>
                      <div className="flex gap-2">
                        {item.hasComparison && (
                          <span className="text-xs px-2 py-1 rounded" style={{ background: 'rgba(239, 68, 68, 0.2)', color: '#fca5a5' }}>vs.</span>
                        )}
                        <span className="text-xs px-2 py-1 rounded" style={{
                          background: getCategoryColor(item.category).bg,
                          color: getCategoryColor(item.category).text
                        }}>
                          {item.category}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </>
        )}

        {/* Instructions */}
        {generatedTitles.length === 0 && (
          <div className="card">
            <h2 className="text-2xl font-bold text-white mb-4">🎯 Optimized BOFU Title Generator</h2>
            <div className="space-y-4 text-purple-200">
              <p className="text-lg">Strategically designed to generate <strong className="text-white">~20K high-value titles</strong> perfect for AI research and blog post creation, positioning AIQ Labs as <strong className="text-white">custom AI builders</strong>.</p>

              <div className="grid md:grid-cols-2 gap-4 mt-6">
                <div className="rounded-lg p-4" style={{ background: 'rgba(139, 92, 246, 0.2)', border: '1px solid rgba(139, 92, 246, 0.3)' }}>
                  <h3 className="text-white font-semibold mb-3">🎯 Smart Mode (~20K Titles)</h3>
                  <ul className="space-y-2 text-sm">
                    <li><strong className="text-purple-300">13 high-authority qualifiers</strong> - "Best", "Top", "Leading", etc.</li>
                    <li><strong className="text-purple-300">35+ AI services</strong> - All your core offerings included</li>
                    <li><strong className="text-purple-300">35 premium industries</strong> - High-value sectors only</li>
                    <li><strong className="text-purple-300">19 optimized templates</strong> - Google AI search friendly</li>
                    <li><strong className="text-purple-300">No "How-To" titles</strong> - Higher commercial intent</li>
                  </ul>
                </div>

                <div className="rounded-lg p-4" style={{ background: 'rgba(236, 72, 153, 0.2)', border: '1px solid rgba(236, 72, 153, 0.3)' }}>
                  <h3 className="text-white font-semibold mb-3">🔥 Perfect for AI Research</h3>
                  <ul className="space-y-2 text-sm">
                    <li><strong className="text-pink-300">Platform Comparisons</strong> - "vs. Zapier", "vs. Make.com", "vs. n8n"</li>
                    <li><strong className="text-pink-300">Authority Keywords</strong> - Optimized for Google AI mentions</li>
                    <li><strong className="text-pink-300">High-Value Industries</strong> - Finance, Healthcare, Legal, Tech</li>
                    <li><strong className="text-pink-300">Commercial Intent</strong> - Ready-to-hire business language</li>
                    <li><strong className="text-pink-300">Blog-Ready Titles</strong> - Perfect for content creation</li>
                  </ul>
                </div>
              </div>

              <div className="p-4 rounded-lg mt-6" style={{ background: 'rgba(34, 197, 94, 0.2)', border: '1px solid rgba(34, 197, 94, 0.3)' }}>
                <p className="text-green-200">
                  <strong className="text-white">Smart Mode Result:</strong> ~20,000 high-quality titles targeting premium industries with maximum Google AI search potential.
                </p>
                <p className="text-green-200 mt-2">
                  <strong className="text-yellow-300">Generate All Mode:</strong> 2M+ titles including all niche markets for complete coverage.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

function App() {
  return <BOFUTitleGenerator />;
}

export default App;
