# AIQ Labs PAA Question Categorizer - Usage Guide

## Overview
This tool categorizes People Also Ask (PAA) questions for AIQ Labs blog content creation, focusing on bottom-of-the-funnel content that aligns with your business context.

## Files Created
- `aiqlabs_paa_categorizer.py` - Main categorization script
- `test_connection.py` - Test script to verify LM Studio connection
- `requirements_aiqlabs.txt` - Python dependencies
- `USAGE_GUIDE.md` - This guide

## Setup

1. **Install Dependencies**
   ```bash
   pip install -r requirements_aiqlabs.txt
   ```

2. **Verify LM Studio Connection**
   ```bash
   python test_connection.py
   ```
   This will test your connection to LM Studio and verify JSON parsing works correctly.

## Usage

### Basic Usage
```bash
python aiqlabs_paa_categorizer.py
```
This will process all questions from your PAA file using default settings.

### Test Mode (Recommended First)
```bash
python aiqlabs_paa_categorizer.py --test
```
This processes only the first 10 questions and shows detailed output for verification.

### Custom File Paths
```bash
python aiqlabs_paa_categorizer.py \
  --questions "your_questions.csv" \
  --categories "your_categories.csv" \
  --context "your_context.txt" \
  --output "your_output.csv"
```

## Input Files Expected

1. **PAA Questions CSV**: Should have a column named "Questions" (or similar)
2. **Blog Categories CSV**: Should have "Parent Category" and "Child Category" columns
3. **Business Context TXT**: Plain text file with your business context

## Output Format

The script generates a CSV with exactly 4 columns as requested:
- `original_question` - The original PAA question
- `parent_category` - Matched parent category (or empty if not relevant)
- `child_category` - Matched child category (or empty if not relevant)  
- `content_focus` - Paragraph explaining how the question connects to AIQ Labs' business

## Key Features

1. **Bottom-of-Funnel Focus**: Prioritizes implementation, solution, and comparison questions
2. **Strict Category Matching**: Only uses your exact category pairs
3. **Business Context Aware**: Uses your AIQ Labs context for relevance filtering
4. **Detailed Content Focus**: Provides substantial paragraphs explaining blog potential
5. **Progress Tracking**: Shows progress during processing
6. **Error Handling**: Graceful handling of API errors and interruptions

## Tips

1. **Start with Test Mode**: Always run `--test` first to verify everything works
2. **Monitor Progress**: The script shows progress every 50 questions
3. **Interrupt Safely**: You can Ctrl+C to stop, and it will save progress
4. **Review Output**: Check the content_focus column to understand AI reasoning

## Troubleshooting

1. **Connection Issues**: Run `test_connection.py` to diagnose
2. **File Not Found**: Check file paths and names match exactly
3. **JSON Parsing Errors**: The script handles these gracefully and marks questions as not relevant
4. **Slow Processing**: Each question takes ~1-2 seconds due to API delay

## Expected Results

- Questions relevant to AIQ Labs will get proper categorization
- Generic AI questions will be marked as not relevant (null categories)
- Bottom-funnel questions will be prioritized
- Each relevant question gets a detailed content focus paragraph

## Next Steps

After running the categorizer:
1. Review the output CSV
2. Filter for non-null categories to get relevant questions
3. Use the content_focus column to guide blog post creation
4. Prioritize questions with the most detailed and specific content focus suggestions
