import csv
import json
from datetime import datetime
from itertools import product

# STRATEGIC APPROACH: Each title should represent a UNIQUE topic worth writing about
# We'll be selective about combinations to avoid near-duplicates

# Core qualifiers that AI search uses for citations
AI_SEARCH_QUALIFIERS = [
    "Best", "Top", "Leading", "#1", "Top-Rated", "Premier", 
    "Most Reliable", "Expert", "Professional", "Trusted"
]

# Your core service offerings - these are your main value propositions
CORE_SERVICES = [
    "AI Development Company",
    "AI Agency",
    "AI Automation Agency",
    "Custom AI Solutions",
    "AI Agent Development",
    "Custom AI Agent Builders",
    "Multi-Agent Systems",
    "Business Automation Solutions",
    "AI Workflow Automation",
    "SaaS Development Company"
]

# Specific solutions - these are concrete products/outcomes
SPECIFIC_SOLUTIONS = {
    "Lead Generation": [
        "AI Lead Generation System",
        "Autonomous Lead Qualification",
        "AI SDR Automation",
        "Lead Scoring AI"
    ],
    "Customer Service": [
        "AI Chatbot Development",
        "Voice AI Agent System",
        "AI Customer Support Automation",
        "24/7 AI Support System"
    ],
    "Marketing": [
        "AI Content Automation",
        "AI SEO System",
        "Social Media AI Automation",
        "AI Email Marketing System"
    ],
    "Sales": [
        "AI Sales Automation",
        "CRM AI Integration",
        "AI Proposal Generation",
        "AI Sales Agent System"
    ],
    "Operations": [
        "Workflow Automation System",
        "AI Document Processing",
        "Custom Internal Software",
        "API Integration Hub"
    ],
    "Analytics": [
        "AI Dashboard Development",
        "Predictive Analytics System",
        "Business Intelligence AI"
    ]
}

# Premium industries (high-value, high-intent)
PREMIUM_INDUSTRIES = [
    # Professional Services (High-Value)
    "Law Firms", "Legal Services", "Accounting Firms", "Management Consulting",
    "Financial Advisors", "Insurance Agencies", "Architecture Firms", "Engineering Firms",
    
    # Healthcare
    "Medical Practices", "Dental Clinics", "Mental Health Practices", "Pharmacies",
    
    # Finance & Investment
    "Banks", "Fintech Companies", "Investment Firms", "Wealth Management Firms",
    "Private Equity Firms", "Venture Capital Firms",
    
    # Real Estate
    "Real Estate Agencies", "Commercial Real Estate Firms", "Property Management Companies",
    
    # Tech & Digital
    "SaaS Companies", "Digital Marketing Agencies", "Software Development Companies",
    "Tech Startups",
    
    # High-Volume Services
    "Construction Companies", "HVAC Companies", "Manufacturing Companies",
    "E-commerce Businesses", "Logistics Companies"
]

# Service business types (for "for [business type]" titles)
SERVICE_BUSINESS_TYPES = [
    "Restaurants", "Hotels", "Auto Dealerships", "Auto Repair Shops",
    "Plumbing Companies", "Electrical Contractors", "Roofing Companies",
    "Landscaping Companies", "Cleaning Services", "Pest Control Companies",
    "Event Planning Companies", "Catering Companies", "Travel Agencies",
    "Fitness Centers", "Yoga Studios", "Daycare Centers", "Tutoring Services"
]

# Platforms for comparison (high commercial intent)
COMPARISON_PLATFORMS = ["Zapier", "Make.com", "n8n", "ChatGPT Plus"]

def generate_titles():
    """Generate strategically distinct titles"""
    titles = set()
    
    # STRATEGY 1: Core Service + Premium Industry (most important)
    # Pattern: "[Qualifier] [Service] for [Industry]"
    print("Generating Strategy 1: Core Service + Premium Industry...")
    for service in CORE_SERVICES:
        for industry in PREMIUM_INDUSTRIES:
            # Use top 3 qualifiers only to avoid repetition
            for qualifier in AI_SEARCH_QUALIFIERS[:3]:
                titles.add(f"{qualifier} {service} for {industry}")
                titles.add(f"{qualifier} {service} for {industry} in 2025")
                titles.add(f"{industry}: {qualifier} {service}")
    
    # STRATEGY 2: Specific Solution + Industry (high-value, outcome-focused)
    # Pattern: "[Qualifier] [Solution] for [Industry]"
    print("Generating Strategy 2: Specific Solutions + Industries...")
    for category, solutions in SPECIFIC_SOLUTIONS.items():
        for solution in solutions:
            for industry in PREMIUM_INDUSTRIES:
                # Top 2 qualifiers only
                for qualifier in AI_SEARCH_QUALIFIERS[:2]:
                    titles.add(f"{qualifier} {solution} for {industry}")
                    titles.add(f"{industry} {solution}: {qualifier} Options")
    
    # STRATEGY 3: Platform Comparisons (VERY high commercial intent)
    # These are unique because they address specific buying decisions
    print("Generating Strategy 3: Platform Comparisons...")
    for platform in COMPARISON_PLATFORMS:
        for service in CORE_SERVICES[:5]:  # Top 5 services only
            for industry in PREMIUM_INDUSTRIES:
                titles.add(f"{service} vs. {platform} for {industry}")
                titles.add(f"Custom AI vs. {platform} for {industry}")
                titles.add(f"Best {platform} Alternative for {industry}")
        
        # Solution-specific comparisons
        for solutions in list(SPECIFIC_SOLUTIONS.values())[:3]:  # Top 3 categories
            for solution in solutions[:2]:  # Top 2 solutions per category
                for industry in PREMIUM_INDUSTRIES[:15]:  # Top 15 industries
                    titles.add(f"{solution} vs. {platform} for {industry}")
    
    # STRATEGY 4: Service Business Types (different angle, service-focused)
    # Pattern: "[Qualifier] [Service] for [Business Type]"
    print("Generating Strategy 4: Service Business Types...")
    for service in CORE_SERVICES[:5]:  # Top 5 core services
        for business in SERVICE_BUSINESS_TYPES:
            for qualifier in AI_SEARCH_QUALIFIERS[:2]:
                titles.add(f"{qualifier} {service} for {business}")
    
    # STRATEGY 5: Solution + Service Business Types
    print("Generating Strategy 5: Solutions for Service Businesses...")
    for solutions in list(SPECIFIC_SOLUTIONS.values()):
        for solution in solutions[:2]:  # Top 2 per category
            for business in SERVICE_BUSINESS_TYPES:
                titles.add(f"{solution} for {business}")
                titles.add(f"Best {solution} for {business}")
    
    # STRATEGY 6: Hiring-focused titles (direct BOFU intent)
    print("Generating Strategy 6: Hiring-Focused Titles...")
    for service in CORE_SERVICES:
        for industry in PREMIUM_INDUSTRIES:
            titles.add(f"Hire {service} for {industry}")
            titles.add(f"Find {service} for Your {industry} Business")
    
    # STRATEGY 7: Transformation & ROI focused (business outcomes)
    print("Generating Strategy 7: Transformation Titles...")
    for service in CORE_SERVICES[:6]:
        for industry in PREMIUM_INDUSTRIES[:20]:
            titles.add(f"Transform Your {industry} Business with {service}")
            titles.add(f"{industry} Digital Transformation: {service}")
    
    # STRATEGY 8: Problem/solution specific titles
    print("Generating Strategy 8: Problem-Solution Titles...")
    pain_points = [
        "Subscription Chaos",
        "Manual Data Entry",
        "Workflow Bottlenecks",
        "Integration Issues",
        "Scaling Challenges"
    ]
    for pain in pain_points:
        for industry in PREMIUM_INDUSTRIES[:15]:
            titles.add(f"Solve {pain} in {industry} with Custom AI")
            titles.add(f"How to Eliminate {pain} in {industry}")
    
    return list(titles)

def categorize_title(title):
    """Determine the category of a title"""
    if any(word in title for word in ["Lead", "SDR", "Qualification"]):
        return "Lead Generation"
    elif any(word in title for word in ["Customer", "Chatbot", "Voice AI", "Support"]):
        return "Customer Service"
    elif any(word in title for word in ["Marketing", "Content", "Social", "SEO", "Email"]):
        return "Marketing"
    elif any(word in title for word in ["Sales", "CRM", "Proposal"]):
        return "Sales"
    elif any(word in title for word in ["Workflow", "Operations", "Dashboard", "Document", "Internal", "API"]):
        return "Operations"
    elif any(word in title for word in ["Analytics", "Intelligence", "Predictive"]):
        return "Analytics"
    elif any(word in title for word in ["vs.", "Alternative", "Comparison"]) or any(p in title for p in COMPARISON_PLATFORMS):
        return "Platform Comparison"
    else:
        return "General AI Development"

def add_metadata(titles):
    """Add metadata to each title"""
    return [
        {
            "id": idx + 1,
            "title": title,
            "category": categorize_title(title),
            "intent": "BOFU",
            "has_comparison": "vs." in title or any(p in title for p in COMPARISON_PLATFORMS),
            "is_service_business": any(b in title for b in SERVICE_BUSINESS_TYPES),
            "has_qualifier": any(q in title for q in AI_SEARCH_QUALIFIERS)
        }
        for idx, title in enumerate(titles)
    ]

def export_csv(data, filename):
    """Export to CSV"""
    with open(filename, 'w', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=data[0].keys())
        writer.writeheader()
        writer.writerows(data)
    print(f"✅ Exported {len(data)} titles to {filename}")

def export_json(data, filename):
    """Export to JSON"""
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    print(f"✅ Exported {len(data)} titles to {filename}")

def print_statistics(data):
    """Print generation statistics"""
    print("\n" + "="*60)
    print("GENERATION STATISTICS")
    print("="*60)
    print(f"Total Unique Titles: {len(data)}")
    print(f"Titles with Comparisons: {sum(1 for t in data if t['has_comparison'])}")
    print(f"Service Business Titles: {sum(1 for t in data if t['is_service_business'])}")
    print(f"Titles with AI Qualifiers: {sum(1 for t in data if t['has_qualifier'])}")
    
    print("\nCategory Breakdown:")
    categories = {}
    for item in data:
        cat = item['category']
        categories[cat] = categories.get(cat, 0) + 1
    
    for cat, count in sorted(categories.items(), key=lambda x: x[1], reverse=True):
        print(f"  {cat}: {count}")
    
    print("\nSample Titles:")
    for item in data[:10]:
        print(f"  • {item['title']}")
    print("="*60 + "\n")

def main():
    print("🚀 AIQ Labs Strategic BOFU Title Generator")
    print("="*60)
    
    # Generate titles
    print("\nGenerating strategic title combinations...\n")
    titles = generate_titles()
    
    # Add metadata
    data = add_metadata(titles)
    
    # Print statistics
    print_statistics(data)
    
    # Export files
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    csv_filename = f"aiq_bofu_titles_{len(data)}_{timestamp}.csv"
    json_filename = f"aiq_bofu_titles_{len(data)}_{timestamp}.json"
    
    export_csv(data, csv_filename)
    export_json(data, json_filename)
    
    print(f"\n✨ Complete! Generated {len(data)} unique, strategic titles.")
    print(f"📊 Each title represents a distinct topic worth creating content for.")

if __name__ == "__main__":
    main()