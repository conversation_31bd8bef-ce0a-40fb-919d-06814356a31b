#!/usr/bin/env python3
"""
Test script to verify VLLM connection and concurrent processing capability
"""

import requests
import json
import time
from concurrent.futures import ThreadPoolExecutor

# Configuration
VLLM_ENDPOINT = "http://**************:4100/v1/chat/completions"
VLLM_MODEL = "Qwen/Qwen3-4B-Instruct-2507"
VLLM_API_KEY = "this-is-my-key"
MAX_CONCURRENT = 10

def test_single_connection():
    """Test single connection to VLLM API."""
    print("Testing single connection to VLLM...")
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {VLLM_API_KEY}"
    }
    
    data = {
        "model": VLLM_MODEL,
        "messages": [
            {"role": "user", "content": "Hello! Please respond with 'VLLM connection successful' if you can read this."}
        ],
        "temperature": 0.3,
        "max_tokens": 50
    }
    
    try:
        response = requests.post(VLLM_ENDPOINT, headers=headers, json=data, timeout=30)
        if response.status_code == 200:
            result = response.json()
            content = result['choices'][0]['message']['content']
            print(f"✅ Single connection successful!")
            print(f"Model response: {content}")
            return True
        else:
            print(f"❌ Error: API returned status code {response.status_code}")
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error connecting to VLLM: {e}")
        return False

def test_json_parsing():
    """Test JSON response parsing."""
    print("\nTesting JSON response parsing...")
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {VLLM_API_KEY}"
    }
    
    prompt = """Please respond with this exact JSON format:
{
  "parent_category": "Newsletter & Content Curation",
  "child_category": "Newsletter Selection Guides",
  "content_focus": "This is a test response to verify JSON parsing works correctly with VLLM."
}"""
    
    data = {
        "model": VLLM_MODEL,
        "messages": [
            {"role": "user", "content": prompt}
        ],
        "temperature": 0.3,
        "max_tokens": 200
    }
    
    try:
        response = requests.post(VLLM_ENDPOINT, headers=headers, json=data, timeout=30)
        if response.status_code == 200:
            result = response.json()
            content = result['choices'][0]['message']['content']
            print(f"Raw response: {content}")
            
            # Try to parse JSON
            start = content.find('{')
            end = content.rfind('}') + 1
            if start != -1 and end > start:
                json_str = content[start:end]
                parsed = json.loads(json_str)
                print(f"✅ JSON parsing successful!")
                print(f"Parsed data: {parsed}")
                return True
            else:
                print(f"❌ Could not find JSON in response")
                return False
        else:
            print(f"❌ Error: API returned status code {response.status_code}")
            return False
    except json.JSONDecodeError as e:
        print(f"❌ JSON parsing error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def make_concurrent_request(request_id: int):
    """Make a single request for concurrent testing."""
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {VLLM_API_KEY}"
    }
    
    data = {
        "model": VLLM_MODEL,
        "messages": [
            {"role": "user", "content": f"This is concurrent request #{request_id}. Please respond with 'Request {request_id} completed successfully'."}
        ],
        "temperature": 0.3,
        "max_tokens": 50
    }
    
    try:
        start_time = time.time()
        response = requests.post(VLLM_ENDPOINT, headers=headers, json=data, timeout=30)
        end_time = time.time()
        
        if response.status_code == 200:
            result = response.json()
            content = result['choices'][0]['message']['content']
            return {
                "request_id": request_id,
                "success": True,
                "response": content,
                "time": end_time - start_time
            }
        else:
            return {
                "request_id": request_id,
                "success": False,
                "error": f"Status code: {response.status_code}",
                "time": end_time - start_time
            }
    except Exception as e:
        return {
            "request_id": request_id,
            "success": False,
            "error": str(e),
            "time": time.time() - start_time if 'start_time' in locals() else 0
        }

def test_concurrent_connections():
    """Test concurrent connections to VLLM API."""
    print(f"\nTesting {MAX_CONCURRENT} concurrent connections...")
    
    start_time = time.time()
    
    with ThreadPoolExecutor(max_workers=MAX_CONCURRENT) as executor:
        # Submit all requests
        futures = [executor.submit(make_concurrent_request, i) for i in range(1, MAX_CONCURRENT + 1)]
        
        # Collect results
        results = []
        for future in futures:
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                print(f"❌ Future error: {e}")
    
    end_time = time.time()
    total_time = end_time - start_time
    
    # Analyze results
    successful = [r for r in results if r['success']]
    failed = [r for r in results if not r['success']]
    
    print(f"\n=== CONCURRENT TEST RESULTS ===")
    print(f"Total requests: {len(results)}")
    print(f"Successful: {len(successful)} ({len(successful)/len(results)*100:.1f}%)")
    print(f"Failed: {len(failed)} ({len(failed)/len(results)*100:.1f}%)")
    print(f"Total time: {total_time:.2f} seconds")
    print(f"Average response time: {sum(r['time'] for r in successful)/len(successful):.2f} seconds" if successful else "N/A")
    
    if failed:
        print(f"\nFailed requests:")
        for fail in failed:
            print(f"  Request {fail['request_id']}: {fail['error']}")
    
    if successful:
        print(f"\nSample successful responses:")
        for success in successful[:3]:
            print(f"  Request {success['request_id']}: {success['response'][:50]}...")
    
    return len(successful) == MAX_CONCURRENT

def main():
    """Run all tests."""
    print("=== VLLM Connection Test ===\n")
    
    # Test basic connection
    connection_ok = test_single_connection()
    
    if not connection_ok:
        print(f"\n❌ Basic connection failed. Please check:")
        print(f"   - VLLM server is running on {VLLM_ENDPOINT}")
        print(f"   - Model '{VLLM_MODEL}' is loaded")
        print(f"   - API key is correct")
        print(f"   - No firewall blocking the connection")
        return
    
    # Test JSON parsing
    json_ok = test_json_parsing()
    
    if not json_ok:
        print(f"\n⚠️  Connection works but JSON parsing needs attention.")
        return
    
    # Test concurrent connections
    concurrent_ok = test_concurrent_connections()
    
    if concurrent_ok:
        print(f"\n✅ All tests passed! Ready to run concurrent categorization.")
        print(f"   - Single connection: ✅")
        print(f"   - JSON parsing: ✅") 
        print(f"   - {MAX_CONCURRENT} concurrent connections: ✅")
    else:
        print(f"\n⚠️  Some concurrent connections failed. You may need to reduce MAX_CONCURRENT.")

if __name__ == "__main__":
    main()
