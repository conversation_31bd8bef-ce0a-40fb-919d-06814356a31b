import csv
import json
from datetime import datetime

# AI Search qualifiers for citations
AI_SEARCH_QUALIFIERS = [
    "Best", "Top", "Leading", "#1", "Top-Rated", "Most Reliable", 
    "Professional", "Trusted", "Enterprise"
]

# Core platform offerings - what AgentiveAIQ provides
CORE_SOLUTIONS = [
    "AI Chatbot Platform",
    "No-Code AI Chatbot Builder",
    "AI Customer Service Platform",
    "AI Sales Assistant Platform",
    "AI Website Chat Widget",
    "Chatbot Automation Platform",
    "AI Agent Platform",
    "Conversational AI Platform"
]

# Specific use case solutions (based on the 9 goals)
USE_CASE_SOLUTIONS = {
    "E-Commerce": [
        "AI Shopping Assistant",
        "E-Commerce Chatbot",
        "AI Product Recommendation System",
        "Shopify AI Chatbot",
        "WooCommerce AI Assistant"
    ],
    "Customer Support": [
        "AI Customer Support Chatbot",
        "24/7 AI Support System",
        "Automated Customer Service",
        "AI Help Desk Solution"
    ],
    "Sales & Lead Gen": [
        "AI Lead Generation Chatbot",
        "AI Sales Assistant",
        "Lead Qualification Bot",
        "Conversational Sales AI"
    ],
    "Real Estate": [
        "AI Real Estate Chatbot",
        "Property Assistant Bot",
        "Real Estate Lead Bot"
    ],
    "Finance": [
        "AI Finance Chatbot",
        "Mortgage Bot Assistant",
        "Financial Services Chatbot"
    ],
    "Education": [
        "AI Course Assistant",
        "Educational Chatbot Platform",
        "AI Tutoring Bot",
        "Student Support Chatbot"
    ],
    "HR & Internal": [
        "AI HR Chatbot",
        "Employee Support Bot",
        "Internal Knowledge Bot"
    ],
    "Training": [
        "AI Onboarding Assistant",
        "Training Bot Platform",
        "Employee Training Chatbot"
    ]
}

# Target industries (high-value sectors for chatbots)
TARGET_INDUSTRIES = [
    # E-Commerce
    "E-Commerce Stores", "Online Retailers", "Shopify Stores", 
    "WooCommerce Sites", "Fashion Retailers", "Electronics Stores",
    
    # Professional Services
    "Law Firms", "Accounting Firms", "Consulting Firms", 
    "Financial Advisors", "Insurance Agencies", "Real Estate Agencies",
    
    # Healthcare
    "Medical Practices", "Dental Clinics", "Mental Health Practices",
    "Veterinary Clinics", "Pharmacies",
    
    # Finance
    "Banks", "Credit Unions", "Fintech Companies", "Mortgage Brokers",
    "Investment Firms", "Lending Companies",
    
    # Tech & SaaS
    "SaaS Companies", "Software Companies", "Tech Startups",
    "Digital Marketing Agencies",
    
    # Education
    "Online Course Creators", "Educational Institutions", 
    "Training Companies", "Coaching Businesses",
    
    # Service Businesses
    "Hotels", "Restaurants", "Fitness Centers", "Spas",
    "Auto Dealerships", "Home Services Companies"
]

# Competitor platforms for comparison
COMPETITOR_PLATFORMS = [
    "Intercom", "Drift", "Zendesk Chat", "LiveChat", 
    "Tidio", "Crisp", "Chatfuel", "ManyChat"
]

# Pain points that AgentiveAIQ solves
PAIN_POINTS = [
    "24/7 Customer Support",
    "Lead Qualification",
    "Website Engagement",
    "Customer Service Automation",
    "Sales Automation",
    "Response Time Issues",
    "Support Ticket Volume"
]

# Platform features (for feature-specific titles)
KEY_FEATURES = [
    "No-Code Chatbot Builder",
    "WYSIWYG Chat Widget",
    "AI Knowledge Base",
    "Shopify Integration",
    "WooCommerce Integration",
    "Custom Branding",
    "Hosted AI Pages",
    "AI Course Platform",
    "Long-Term Memory",
    "Sentiment Analysis"
]

def generate_titles():
    """Generate strategically distinct BOFU titles"""
    titles = set()
    
    # STRATEGY 1: Core Solution + Industry
    print("Generating Strategy 1: Core Solutions for Industries...")
    for solution in CORE_SOLUTIONS:
        for industry in TARGET_INDUSTRIES:
            # Top qualifiers only
            for qualifier in AI_SEARCH_QUALIFIERS[:3]:
                titles.add(f"{qualifier} {solution} for {industry}")
                titles.add(f"{qualifier} {solution} for {industry} in 2025")
            # Direct hiring intent
            titles.add(f"{solution} for {industry}")
            titles.add(f"Choose {solution} for Your {industry} Business")
    
    # STRATEGY 2: Use Case Solutions + Industry
    print("Generating Strategy 2: Use Case Solutions for Industries...")
    
    # E-Commerce solutions for e-commerce industries only
    ecommerce_industries = ["E-Commerce Stores", "Online Retailers", "Shopify Stores", 
                           "WooCommerce Sites", "Fashion Retailers", "Electronics Stores"]
    for solution in USE_CASE_SOLUTIONS["E-Commerce"]:
        for industry in ecommerce_industries:
            for qualifier in AI_SEARCH_QUALIFIERS[:2]:
                titles.add(f"{qualifier} {solution} for {industry}")
            titles.add(f"{solution} for {industry}")
    
    # Real Estate solutions for real estate only
    for solution in USE_CASE_SOLUTIONS["Real Estate"]:
        for qualifier in AI_SEARCH_QUALIFIERS[:2]:
            titles.add(f"{qualifier} {solution} for Real Estate Agencies")
        titles.add(f"{solution} for Real Estate Agencies")
        titles.add(f"{solution} for Commercial Real Estate Firms")
    
    # Finance solutions for finance industries only
    finance_industries = ["Banks", "Credit Unions", "Fintech Companies", "Mortgage Brokers", 
                         "Investment Firms", "Lending Companies", "Financial Advisors", "Insurance Agencies"]
    for solution in USE_CASE_SOLUTIONS["Finance"]:
        for industry in finance_industries:
            for qualifier in AI_SEARCH_QUALIFIERS[:2]:
                titles.add(f"{qualifier} {solution} for {industry}")
            titles.add(f"{solution} for {industry}")
    
    # Education solutions for education industries only
    education_industries = ["Online Course Creators", "Educational Institutions", 
                           "Training Companies", "Coaching Businesses"]
    for solution in USE_CASE_SOLUTIONS["Education"]:
        for industry in education_industries:
            for qualifier in AI_SEARCH_QUALIFIERS[:2]:
                titles.add(f"{qualifier} {solution} for {industry}")
            titles.add(f"{solution} for {industry}")
    
    # Customer Support and Sales solutions work broadly across industries
    for solution in USE_CASE_SOLUTIONS["Customer Support"]:
        for industry in TARGET_INDUSTRIES:
            for qualifier in AI_SEARCH_QUALIFIERS[:2]:
                titles.add(f"{qualifier} {solution} for {industry}")
            titles.add(f"{solution} for {industry}")
    
    for solution in USE_CASE_SOLUTIONS["Sales & Lead Gen"]:
        for industry in TARGET_INDUSTRIES:
            for qualifier in AI_SEARCH_QUALIFIERS[:2]:
                titles.add(f"{qualifier} {solution} for {industry}")
            titles.add(f"{solution} for {industry}")
    
    # HR & Training solutions for all business types (internal use)
    for solution in USE_CASE_SOLUTIONS["HR & Internal"]:
        for industry in TARGET_INDUSTRIES[:20]:
            titles.add(f"{solution} for {industry}")
            titles.add(f"Best {solution} for {industry}")
    
    for solution in USE_CASE_SOLUTIONS["Training"]:
        for industry in TARGET_INDUSTRIES[:20]:
            titles.add(f"{solution} for {industry}")
            titles.add(f"Best {solution} for {industry}")
    
    # STRATEGY 3: Platform Comparisons (HIGH VALUE)
    print("Generating Strategy 3: Competitor Comparisons...")
    for competitor in COMPETITOR_PLATFORMS:
        # Core platform comparisons - these work for any industry
        for solution in CORE_SOLUTIONS[:4]:
            for industry in TARGET_INDUSTRIES:
                titles.add(f"{solution} vs. {competitor} for {industry}")
                titles.add(f"Best {competitor} Alternative for {industry}")
        
        # Use case specific comparisons - ONLY match relevant industries
        # E-Commerce solutions only for e-commerce industries
        for solution in USE_CASE_SOLUTIONS["E-Commerce"][:2]:
            ecommerce_industries = ["E-Commerce Stores", "Online Retailers", "Shopify Stores", 
                                   "WooCommerce Sites", "Fashion Retailers", "Electronics Stores"]
            for industry in ecommerce_industries:
                titles.add(f"{solution} vs. {competitor} for {industry}")
        
        # Real Estate solutions only for real estate
        for solution in USE_CASE_SOLUTIONS["Real Estate"]:
            titles.add(f"{solution} vs. {competitor} for Real Estate Agencies")
        
        # Finance solutions only for finance industries
        for solution in USE_CASE_SOLUTIONS["Finance"][:2]:
            finance_industries = ["Banks", "Credit Unions", "Fintech Companies", "Mortgage Brokers", 
                                 "Investment Firms", "Lending Companies"]
            for industry in finance_industries:
                titles.add(f"{solution} vs. {competitor} for {industry}")
        
        # Education solutions only for education industries
        for solution in USE_CASE_SOLUTIONS["Education"][:2]:
            education_industries = ["Online Course Creators", "Educational Institutions", 
                                   "Training Companies", "Coaching Businesses"]
            for industry in education_industries:
                titles.add(f"{solution} vs. {competitor} for {industry}")
        
        # Customer Support & Sales work broadly but not with hyper-specific industries
        for solution in USE_CASE_SOLUTIONS["Customer Support"][:2]:
            for industry in TARGET_INDUSTRIES[:20]:  # Broader applicability
                titles.add(f"{solution} vs. {competitor} for {industry}")
        
        for solution in USE_CASE_SOLUTIONS["Sales & Lead Gen"][:2]:
            for industry in TARGET_INDUSTRIES[:20]:  # Broader applicability
                titles.add(f"{solution} vs. {competitor} for {industry}")
        
        # General alternatives
        titles.add(f"{competitor} Alternative: AgentiveAIQ")
        titles.add(f"Best {competitor} Alternatives for Small Business")
        titles.add(f"Why Switch from {competitor} to AgentiveAIQ")
    
    # STRATEGY 4: Pain Point Solutions
    print("Generating Strategy 4: Pain Point Solutions...")
    for pain in PAIN_POINTS:
        for industry in TARGET_INDUSTRIES:
            titles.add(f"Solve {pain} for {industry} with AI Chatbots")
            titles.add(f"{industry} Solution: {pain}")
            titles.add(f"How to Implement {pain} in {industry}")
    
    # STRATEGY 5: Feature-Specific Titles
    print("Generating Strategy 5: Feature-Focused Titles...")
    for feature in KEY_FEATURES:
        for industry in TARGET_INDUSTRIES[:20]:
            titles.add(f"{feature} for {industry}")
            titles.add(f"Best {feature} for {industry}")
    
    # STRATEGY 6: Integration-Specific
    print("Generating Strategy 6: Integration-Focused Titles...")
    ecommerce_industries = [
        "Shopify Stores", "WooCommerce Sites", "E-Commerce Stores",
        "Online Retailers", "Fashion Retailers", "Electronics Stores"
    ]
    
    for industry in ecommerce_industries:
        titles.add(f"Best AI Chatbot for {industry}")
        titles.add(f"AI Shopping Assistant for {industry}")
        titles.add(f"Shopify AI Integration for {industry}")
        titles.add(f"WooCommerce AI Chatbot for {industry}")
        titles.add(f"E-Commerce AI Automation for {industry}")
    
    # STRATEGY 7: Business Outcome Focused
    print("Generating Strategy 7: Outcome-Focused Titles...")
    outcomes = [
        "Increase Sales", "Reduce Support Costs", "Improve Engagement",
        "Generate More Leads", "Boost Conversions", "Scale Support"
    ]
    
    for outcome in outcomes:
        for industry in TARGET_INDUSTRIES[:15]:
            titles.add(f"How to {outcome} in {industry} with AI Chatbots")
            titles.add(f"{outcome} for {industry}: AI Chatbot Guide")
    
    # STRATEGY 8: Buying Decision Titles
    print("Generating Strategy 8: Buying Decision Support...")
    for industry in TARGET_INDUSTRIES:
        titles.add(f"Chatbot Platform Comparison for {industry}")
        titles.add(f"Choosing an AI Chatbot for {industry}")
        titles.add(f"AI Chatbot Pricing for {industry}")
    
    # STRATEGY 9: Industry-Specific Deep Dives
    print("Generating Strategy 9: Industry-Specific Solutions...")
    for industry in TARGET_INDUSTRIES:
        titles.add(f"{industry} Chatbot: Complete Guide")
        titles.add(f"AI Automation for {industry}")
        titles.add(f"{industry} Customer Service Automation")
        titles.add(f"Best Customer Support Solution for {industry}")
    
    # STRATEGY 10: Plan/Pricing Focused
    print("Generating Strategy 10: Pricing & Plan Titles...")
    plan_types = ["Affordable", "Enterprise", "Small Business", "Agency"]
    
    for plan_type in plan_types:
        for industry in TARGET_INDUSTRIES[:20]:
            titles.add(f"{plan_type} AI Chatbot for {industry}")
            titles.add(f"{plan_type} Chatbot Platform for {industry}")
    
    # STRATEGY 11: No-Code Angle (Key Differentiator)
    print("Generating Strategy 11: No-Code Positioning...")
    for industry in TARGET_INDUSTRIES:
        titles.add(f"No-Code Chatbot Solution for {industry}")
        titles.add(f"Build AI Chatbot for {industry} Without Coding")
        titles.add(f"Easy Chatbot Builder for {industry}")
    
    # STRATEGY 12: Course/Education Specific (Unique Feature)
    print("Generating Strategy 12: Educational Use Cases...")
    education_terms = [
        "Online Course Creators", "Educational Institutions", 
        "Coaching Businesses", "Training Companies"
    ]
    
    for industry in education_terms:
        titles.add(f"AI Course Assistant for {industry}")
        titles.add(f"Student Support Chatbot for {industry}")
        titles.add(f"AI Tutoring Platform for {industry}")
        titles.add(f"Course Chatbot for {industry}")
    
    return list(titles)

def categorize_title(title):
    """Categorize titles by use case"""
    title_lower = title.lower()
    
    if any(word in title_lower for word in ["e-commerce", "shopping", "shopify", "woocommerce", "product", "retail"]):
        return "E-Commerce"
    elif any(word in title_lower for word in ["support", "customer service", "help desk", "ticket"]):
        return "Customer Support"
    elif any(word in title_lower for word in ["sales", "lead", "qualification", "conversion"]):
        return "Sales & Lead Generation"
    elif any(word in title_lower for word in ["real estate", "property"]):
        return "Real Estate"
    elif any(word in title_lower for word in ["finance", "mortgage", "lending", "bank"]):
        return "Finance"
    elif any(word in title_lower for word in ["education", "course", "student", "tutoring", "learning"]):
        return "Education"
    elif any(word in title_lower for word in ["hr", "employee", "internal"]):
        return "HR & Internal"
    elif any(word in title_lower for word in ["training", "onboarding"]):
        return "Training"
    elif any(word in title_lower for word in ["vs.", "alternative", "comparison"]) or \
         any(comp.lower() in title_lower for comp in COMPETITOR_PLATFORMS):
        return "Platform Comparison"
    else:
        return "General Chatbot Platform"

def add_metadata(titles):
    """Add metadata to each title"""
    return [
        {
            "id": idx + 1,
            "title": title,
            "category": categorize_title(title),
            "intent": "BOFU",
            "has_comparison": "vs." in title or any(comp in title for comp in COMPETITOR_PLATFORMS),
            "is_industry_specific": any(ind in title for ind in TARGET_INDUSTRIES),
            "has_qualifier": any(q in title for q in AI_SEARCH_QUALIFIERS),
            "is_ecommerce": any(word in title.lower() for word in ["shopify", "woocommerce", "e-commerce"]),
            "is_no_code_focused": "no-code" in title.lower() or "without coding" in title.lower()
        }
        for idx, title in enumerate(titles)
    ]

def export_csv(data, filename):
    """Export to CSV"""
    with open(filename, 'w', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=data[0].keys())
        writer.writeheader()
        writer.writerows(data)
    print(f"✅ Exported {len(data)} titles to {filename}")

def export_json(data, filename):
    """Export to JSON"""
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    print(f"✅ Exported {len(data)} titles to {filename}")

def print_statistics(data):
    """Print generation statistics"""
    print("\n" + "="*70)
    print("AGENTIVEAIQ BOFU TITLE GENERATION STATISTICS")
    print("="*70)
    print(f"Total Unique Titles: {len(data):,}")
    print(f"Titles with Platform Comparisons: {sum(1 for t in data if t['has_comparison']):,}")
    print(f"Industry-Specific Titles: {sum(1 for t in data if t['is_industry_specific']):,}")
    print(f"E-Commerce Integration Titles: {sum(1 for t in data if t['is_ecommerce']):,}")
    print(f"No-Code Focused Titles: {sum(1 for t in data if t['is_no_code_focused']):,}")
    print(f"Titles with AI Qualifiers: {sum(1 for t in data if t['has_qualifier']):,}")
    
    print("\nCategory Breakdown:")
    categories = {}
    for item in data:
        cat = item['category']
        categories[cat] = categories.get(cat, 0) + 1
    
    for cat, count in sorted(categories.items(), key=lambda x: x[1], reverse=True):
        print(f"  {cat}: {count:,}")
    
    print("\nSample Titles by Category:")
    for cat in sorted(set(item['category'] for item in data)):
        samples = [item['title'] for item in data if item['category'] == cat][:3]
        print(f"\n  {cat}:")
        for sample in samples:
            print(f"    • {sample}")
    
    print("\n" + "="*70 + "\n")

def main():
    print("🤖 AgentiveAIQ Strategic BOFU Title Generator")
    print("="*70)
    print("Generating high-intent titles for AI chatbot platform positioning\n")
    
    # Generate titles
    print("Generating strategic title combinations...\n")
    titles = generate_titles()
    
    # Add metadata
    data = add_metadata(titles)
    
    # Print statistics
    print_statistics(data)
    
    # Export files
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    csv_filename = f"agentiveaiq_bofu_titles_{len(data)}_{timestamp}.csv"
    json_filename = f"agentiveaiq_bofu_titles_{len(data)}_{timestamp}.json"
    
    export_csv(data, csv_filename)
    export_json(data, json_filename)
    
    print(f"✨ Complete! Generated {len(data):,} unique, strategic titles.")
    print(f"📊 Each title targets businesses ready to implement AI chatbot solutions.")
    print(f"\nKey Focus Areas:")
    print(f"  • Platform comparisons vs. Intercom, Drift, Zendesk, etc.")
    print(f"  • E-commerce integrations (Shopify/WooCommerce)")
    print(f"  • No-code positioning (key differentiator)")
    print(f"  • 9 use case categories (E-Commerce, Support, Sales, etc.)")
    print(f"  • 35+ target industries")

if __name__ == "__main__":
    main()