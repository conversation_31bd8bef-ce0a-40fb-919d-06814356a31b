#!/usr/bin/env python3
"""
AIQ Labs PAA Question Categorizer - Sequential Batch Version

This version processes batches sequentially but with concurrent questions within each batch.
This should be more stable for large datasets.
"""

import csv
import json
import argparse
import requests
import time
import os
import asyncio
import aiohttp
from typing import List, Dict, Tuple
from concurrent.futures import ThreadPoolExecutor
import threading

# Configuration for VLLM API
VLLM_ENDPOINT = "http://192.222.59.118:4100/v1/chat/completions"
VLLM_MODEL = "Qwen/Qwen3-4B-Instruct-2507"
VLLM_API_KEY = "this-is-my-key"
MAX_CONCURRENT = 5

def read_paa_questions(filename: str) -> List[str]:
    """Read PAA questions from CSV file."""
    questions = []
    with open(filename, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            # Handle different possible column names
            if 'PAA Title' in row:
                questions.append(row['PAA Title'])
            elif 'Questions' in row:
                questions.append(row['Questions'])
            elif 'original_question' in row:
                questions.append(row['original_question'])
            elif 'PAA_Question' in row:
                questions.append(row['PAA_Question'])
            else:
                # Use first column value
                first_column = next(iter(row.values()))
                questions.append(first_column)
    return questions

def read_blog_categories(filename: str) -> List[Tuple[str, str]]:
    """Read blog categories from CSV file - handles both formats."""
    categories = []
    with open(filename, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            # Handle both formats: "Parent Category" or "parent_category"
            if 'Parent Category' in row:
                parent = row['Parent Category'].strip()
                child = row['Child Category'].strip()
            elif 'parent_category' in row:
                parent = row['parent_category'].strip()
                child = row['child_category'].strip()
            else:
                # Fallback to first two columns
                values = list(row.values())
                parent = values[0].strip() if len(values) > 0 else ""
                child = values[1].strip() if len(values) > 1 else ""
            categories.append((parent, child))
    return categories

def read_business_context(filename: str) -> str:
    """Read business context from text file."""
    with open(filename, 'r', encoding='utf-8') as file:
        return file.read()

def get_categories_context(valid_categories: List[Tuple[str, str]]) -> str:
    """Return formatted category pairs for the AI to choose from."""
    context = "You MUST choose from one of these EXACT parent-child category pairs:\n\n"
    
    for i, (parent, child) in enumerate(valid_categories, 1):
        context += f"{i}. Parent: \"{parent}\" | Child: \"{child}\"\n"
    
    context += "\nYou are REQUIRED to select one of these exact pairs. Do not create new categories or modify the names."
    return context

def query_vllm_api(prompt: str, max_tokens: int = 600) -> str:
    """Query the VLLM API with retry logic."""
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {VLLM_API_KEY}"
    }
    
    data = {
        "model": VLLM_MODEL,
        "messages": [
            {"role": "user", "content": prompt}
        ],
        "temperature": 0.3,
        "max_tokens": max_tokens
    }
    
    max_retries = 3
    for attempt in range(max_retries):
        try:
            response = requests.post(VLLM_ENDPOINT, headers=headers, json=data, timeout=30)
            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content']
            else:
                if attempt < max_retries - 1:
                    time.sleep(2)
                    continue
                return "Error processing request"
        except requests.exceptions.Timeout:
            if attempt < max_retries - 1:
                time.sleep(5)
                continue
            return "Error: Request timeout"
        except Exception as e:
            if attempt < max_retries - 1:
                time.sleep(3)
                continue
            return "Error connecting to VLLM"

def categorize_question(question: str, valid_categories: List[Tuple[str, str]], business_context: str) -> Dict:
    """Categorize a single PAA question for AIQ Labs blog content."""
    
    prompt = f"""
You are an AI assistant helping AIQ Labs categorize People Also Ask (PAA) questions for blog content creation.

BUSINESS CONTEXT:
{business_context}

CRITICAL INSTRUCTIONS:
You MUST select from these EXACT parent-child category pairs. DO NOT create new categories or modify names.

{get_categories_context(valid_categories)}

FOCUS: We want bottom-of-the-funnel content that helps prospects who are already interested in custom AI development, AI workflow automation, or business process optimization and are looking for implementation guidance, comparisons, or specific solutions.

Question: "{question}"

TASK:
1. Determine if this question is relevant to AIQ Labs' business and suitable for a blog article
2. If relevant, categorize it into ONE of the exact parent-child category pairs listed above
3. If NOT relevant to AIQ Labs' business, set both categories to null
4. Provide a content focus paragraph explaining how this connects to AIQ Labs' solutions

You MUST respond with this EXACT JSON format:
{{
  "parent_category": "Copy exact parent name from list above" OR null if not relevant,
  "child_category": "Copy exact child name from list above" OR null if not relevant,
  "content_focus": "A paragraph explaining how this question connects to AIQ Labs' business context and how it can be used for a blog post. Focus on specific AIQ Labs solutions, benefits, and implementation approaches. If not relevant, state 'Not relevant to AIQ Labs business context.'"
}}

REMEMBER:
- Only use the exact category names from the numbered list above
- Focus on questions that help prospects understand custom AI development, workflow automation, business process optimization, or bespoke AI solutions
- If the question is too general or not relevant to AIQ Labs' custom AI development services, use null for both categories
- The content_focus should be a substantial paragraph (3-4 sentences) that clearly connects the question to AIQ Labs' specific capabilities
"""

    response = query_vllm_api(prompt, max_tokens=600)
    
    # Try to extract JSON from response
    try:
        start = response.find('{')
        end = response.rfind('}') + 1
        if start != -1 and end > start:
            json_str = response[start:end]
            result = json.loads(json_str)
            
            parent = result.get("parent_category")
            child = result.get("child_category")
            
            if parent is None and child is None:
                return result
            
            if parent and child:
                parent = parent.strip()
                child = child.strip()
                valid_set = set(valid_categories)
                if (parent, child) in valid_set:
                    return result
            
            return {
                "parent_category": None,
                "child_category": None,
                "content_focus": "Not relevant to AIQ Labs business context."
            }
        else:
            return {
                "parent_category": None,
                "child_category": None,
                "content_focus": "Could not parse AI response."
            }
    except json.JSONDecodeError:
        return {
            "parent_category": None,
            "child_category": None,
            "content_focus": "JSON parsing error in AI response."
        }

def process_single_question(args):
    """Process a single question - for use with ThreadPoolExecutor."""
    idx, question, valid_categories, business_context = args
    try:
        result = categorize_question(question, valid_categories, business_context)
        result["original_question"] = question
        return (idx, result)
    except Exception as e:
        error_result = {
            "original_question": question,
            "parent_category": None,
            "child_category": None,
            "content_focus": f"Processing error: {str(e)}"
        }
        return (idx, error_result)

def process_questions_sequential_batches(questions: List[str], valid_categories: List[Tuple[str, str]], 
                                        business_context: str, test_mode: bool = False, concurrent: int = 5) -> List[Dict]:
    """Process questions in sequential batches with concurrent processing within each batch."""
    
    if test_mode:
        questions = questions[:20]
        print(f"Running in test mode. Processing first {len(questions)} questions...")
    else:
        print(f"Processing {len(questions)} questions with {concurrent} concurrent connections per batch...")
    
    # Create smaller batches for sequential processing
    batch_size = 500  # Process 500 questions at a time
    all_results = []
    
    for batch_start in range(0, len(questions), batch_size):
        batch_end = min(batch_start + batch_size, len(questions))
        batch_questions = questions[batch_start:batch_end]
        batch_num = (batch_start // batch_size) + 1
        total_batches = (len(questions) + batch_size - 1) // batch_size
        
        print(f"\nProcessing batch {batch_num}/{total_batches} ({len(batch_questions)} questions)")
        
        # Prepare arguments for concurrent processing within this batch
        args_list = [
            (batch_start + i, question, valid_categories, business_context)
            for i, question in enumerate(batch_questions)
        ]
        
        # Process this batch concurrently
        batch_results = []
        with ThreadPoolExecutor(max_workers=concurrent) as executor:
            futures = [executor.submit(process_single_question, args) for args in args_list]
            
            completed = 0
            for future in futures:
                try:
                    result = future.result()
                    batch_results.append(result)
                    completed += 1
                    
                    # Show progress every 50 questions
                    if completed % 50 == 0:
                        print(f"  [OK] {completed}/{len(batch_questions)} questions completed in batch {batch_num}")
                        
                except Exception as e:
                    print(f"  [ERROR] Error in batch {batch_num}: {e}")
        
        all_results.extend(batch_results)
        print(f"  [COMPLETE] Batch {batch_num} completed: {len(batch_results)} questions processed")
        
        # Small delay between batches to avoid overwhelming the server
        if batch_num < total_batches:
            time.sleep(2)
    
    # Sort results by original index to maintain order
    all_results.sort(key=lambda x: x[0])
    
    # Extract just the result dictionaries
    final_results = [result for _, result in all_results]
    
    return final_results

def save_results_to_csv(results: List[Dict], filename: str):
    """Save results to CSV in the requested format."""
    fieldnames = ["original_question", "parent_category", "child_category", "content_focus"]
    
    with open(filename, 'w', newline='', encoding='utf-8') as file:
        writer = csv.DictWriter(file, fieldnames=fieldnames)
        writer.writeheader()
        
        for result in results:
            csv_row = {}
            for field in fieldnames:
                value = result.get(field)
                csv_row[field] = value if value is not None else ""
            writer.writerow(csv_row)
    
    print(f"Results saved to {filename}")

def main():
    """Main function to run the AIQ Labs PAA categorizer."""
    parser = argparse.ArgumentParser(description="Categorize PAA questions for AIQ Labs blog content (Sequential Batch Version)")
    parser.add_argument("--test", action="store_true", help="Run in test mode with first 20 questions")
    parser.add_argument("--questions", default="AIQ_Labs_0926.csv", 
                       help="Input CSV file with PAA questions")
    parser.add_argument("--categories", default="AIQ Labs Blog Categories - Sheet1.csv", 
                       help="CSV file with blog categories")
    parser.add_argument("--context", default="AIQ Labs Context.txt", 
                       help="Text file with business context")
    parser.add_argument("--output", default="aiqlabs_categorized_questions.csv", 
                       help="Output CSV file")
    parser.add_argument("--concurrent", type=int, default=5, 
                       help="Number of concurrent connections per batch (default: 5)")
    
    args = parser.parse_args()
    
    print("=== AIQ Labs PAA Question Categorizer (Sequential Batch Version) ===\n")
    
    # Check if input files exist
    for file_path in [args.questions, args.categories, args.context]:
        if not os.path.exists(file_path):
            print(f"Error: File '{file_path}' not found!")
            return
    
    try:
        # Read business context
        print("Loading business context...")
        business_context = read_business_context(args.context)
        
        # Read valid categories
        print("Loading blog categories...")
        valid_categories = read_blog_categories(args.categories)
        print(f"Loaded {len(valid_categories)} valid category pairs")
        
        # Read questions
        print("Loading PAA questions...")
        questions = read_paa_questions(args.questions)
        print(f"Loaded {len(questions)} questions to categorize")
        
        # Process questions with sequential batch processing
        start_time = time.time()
        results = process_questions_sequential_batches(questions, valid_categories, business_context, args.test, args.concurrent)
        end_time = time.time()
        
        # Save results
        save_results_to_csv(results, args.output)
        
        # Print summary
        relevant_count = sum(1 for r in results if r.get('parent_category') is not None)
        processing_time = end_time - start_time
        
        print(f"\n=== SUMMARY ===")
        print(f"Total questions processed: {len(results)}")
        print(f"Relevant for blog content: {relevant_count} ({relevant_count/len(results)*100:.1f}%)")
        print(f"Not relevant: {len(results) - relevant_count} ({(len(results) - relevant_count)/len(results)*100:.1f}%)")
        print(f"Processing time: {processing_time/60:.1f} minutes")
        print(f"Average time per question: {processing_time/len(results):.2f} seconds")
        print(f"\nOutput saved to: {args.output}")
        
    except Exception as e:
        print(f"Error during processing: {e}")
        return

if __name__ == "__main__":
    main()