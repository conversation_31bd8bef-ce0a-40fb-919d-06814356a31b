#!/usr/bin/env python3
"""
Analyze the results of the categorization filtering.
"""

import pandas as pd

def main():
    # Load the files
    matched = pd.read_csv('matched_categories.csv')
    unmatched = pd.read_csv('unmatched_categories.csv')
    original = pd.read_csv('output - Sheet1.csv')

    total = len(matched) + len(unmatched)

    print('=== PAA QUESTION CATEGORIZATION RESULTS ===')
    print(f'Total questions processed: {len(original):,}')
    print(f'Perfectly matched: {len(matched):,} ({len(matched)/total*100:.1f}%)')
    print(f'Need reassignment: {len(unmatched):,} ({len(unmatched)/total*100:.1f}%)')
    print()

    # Analyze unmatched categories
    print('=== ANALYSIS OF UNMATCHED CATEGORIES ===')
    unique_parents = unmatched['parent_category'].value_counts()
    unique_children = unmatched['child_category'].value_counts()

    print(f'Unique parent categories in unmatched data: {len(unique_parents)}')
    print('Top 10 unmatched parent categories:')
    for parent, count in unique_parents.head(10).items():
        print(f'  "{parent}": {count} questions')

    print(f'\nUnique child categories in unmatched data: {len(unique_children)}')
    print('Top 10 unmatched child categories:')
    for child, count in unique_children.head(10).items():
        print(f'  "{child}": {count} questions')

    # Count blank categories
    blank_parents = len(unmatched[unmatched['parent_category'] == ''])
    blank_children = len(unmatched[unmatched['child_category'] == ''])
    print(f'\nBlank categories:')
    print(f'  Blank parent categories: {blank_parents}')
    print(f'  Blank child categories: {blank_children}')

if __name__ == "__main__":
    main()
