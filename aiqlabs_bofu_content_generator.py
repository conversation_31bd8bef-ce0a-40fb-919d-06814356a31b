#!/usr/bin/env python3
"""
AIQ Labs BOFU Content Focus Generator

This script takes already-categorized PAA questions and generates bottom-of-funnel
content focus angles that are SEO-heavy and naturally transition to AIQ Labs solutions.
"""

import csv
import json
import argparse
import requests
import time
import os
from typing import List, Dict
from concurrent.futures import ThreadPoolExecutor
import threading

# Configuration for VLLM API
VLLM_ENDPOINT = "http://**************:4100/v1/chat/completions"
VLLM_MODEL = "Qwen/Qwen3-4B-Instruct-2507"
VLLM_API_KEY = "this-is-my-key"
MAX_CONCURRENT = 5

def read_categorized_questions(filename: str) -> List[Dict]:
    """Read already categorized questions from CSV file."""
    questions = []
    with open(filename, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            # Handle different possible column names
            question_data = {}
            
            # Get the question/title
            if 'original_question' in row:
                question_data['question'] = row['original_question']
            elif 'PAA Title' in row:
                question_data['question'] = row['PAA Title']
            elif 'title' in row:
                question_data['question'] = row['title']
            else:
                question_data['question'] = list(row.values())[0]
            
            # Get parent category
            if 'parent_category' in row:
                question_data['parent_category'] = row['parent_category']
            elif 'Parent Category' in row:
                question_data['parent_category'] = row['Parent Category']
            else:
                question_data['parent_category'] = ""
            
            # Get child category
            if 'child_category' in row:
                question_data['child_category'] = row['child_category']
            elif 'Child Category' in row:
                question_data['child_category'] = row['Child Category']
            else:
                question_data['child_category'] = ""
            
            # Get existing content focus if available
            if 'content_focus' in row:
                question_data['existing_content_focus'] = row['content_focus']
            else:
                question_data['existing_content_focus'] = ""
            
            questions.append(question_data)
    return questions

def read_business_context(filename: str) -> str:
    """Read business context from text file."""
    with open(filename, 'r', encoding='utf-8') as file:
        return file.read()

def query_vllm_api(prompt: str, max_tokens: int = 800) -> str:
    """Query the VLLM API with retry logic."""
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {VLLM_API_KEY}"
    }
    
    data = {
        "model": VLLM_MODEL,
        "messages": [
            {"role": "user", "content": prompt}
        ],
        "temperature": 0.3,
        "max_tokens": max_tokens
    }
    
    max_retries = 3
    for attempt in range(max_retries):
        try:
            response = requests.post(VLLM_ENDPOINT, headers=headers, json=data, timeout=30)
            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content']
            else:
                if attempt < max_retries - 1:
                    time.sleep(2)
                    continue
                return "Error processing request"
        except requests.exceptions.Timeout:
            if attempt < max_retries - 1:
                time.sleep(5)
                continue
            return "Error: Request timeout"
        except Exception as e:
            if attempt < max_retries - 1:
                time.sleep(3)
                continue
            return "Error connecting to VLLM"

def generate_bofu_content_focus(question_data: Dict, business_context: str) -> str:
    """Generate a BOFU-focused content angle for the question."""
    
    question = question_data['question']
    parent_category = question_data['parent_category']
    child_category = question_data['child_category']
    
    prompt = f"""
You are a content strategist for AIQ Labs creating bottom-of-funnel (BOFU) content angles.

BUSINESS CONTEXT:
{business_context}

QUESTION: "{question}"
CATEGORY: {parent_category} > {child_category}

TASK: Create a BOFU content focus that naturally transitions this question toward AIQ Labs custom AI development solutions.

TARGET AUDIENCE: Business owners, operations managers, and decision-makers evaluating custom AI automation and development services

REQUIREMENTS:
- Target prospects ready to implement custom AI workflows, automations, and systems for their business
- Focus on implementation, ROI, competitive advantages, and measurable business outcomes from custom AI solutions
- Include SEO-rich language targeting decision-makers researching "custom AI development," "AI workflow automation," "bespoke AI systems," "AI business process automation"
- Connect to AIQ Labs' core differentiators: custom code development, multi-agent systems, owned vs rented solutions, enterprise-grade automation, unified dashboards
- Emphasize business benefits: cost reduction (60-80% decrease in SaaS subscriptions), time savings (20-40 hours/week), revenue growth, rapid ROI (30-60 days)
- Address implementation concerns, competitive positioning against no-code tools and typical AI agencies, and decision-making factors
- Do NOT make up case studies, statistics, or fake data
- Natural transition that acknowledges the original question but steers toward custom AI development and automation considerations

CONTENT FOCUS APPROACH:
1. Acknowledge the original question's intent and provide valuable context
2. Pivot to business implementation and decision-making angles relevant to custom AI development
3. Highlight specific AIQ Labs capabilities that address these business automation needs
4. Include SEO-rich terms that decision-makers search for when evaluating custom AI development services
5. End with implementation considerations, competitive advantages, or ROI focus that drives action toward custom AI solutions

Write a 4-5 sentence paragraph that transforms this question into a BOFU content angle focused on helping business decision-makers understand implementation, benefits, competitive positioning, and ROI of custom AI development solutions like AIQ Labs' bespoke automation systems.

CONTENT FOCUS:
"""

    response = query_vllm_api(prompt, max_tokens=800)
    
    # Clean up the response to extract just the content focus
    if "CONTENT FOCUS:" in response:
        content_focus = response.split("CONTENT FOCUS:")[-1].strip()
    else:
        content_focus = response.strip()
    
    return content_focus

def process_single_question(args):
    """Process a single question - for use with ThreadPoolExecutor."""
    idx, question_data, business_context = args
    try:
        new_content_focus = generate_bofu_content_focus(question_data, business_context)
        
        result = {
            "original_question": question_data['question'],
            "parent_category": question_data['parent_category'],
            "child_category": question_data['child_category'],
            "content_focus": new_content_focus
        }
        return (idx, result)
    except Exception as e:
        error_result = {
            "original_question": question_data['question'],
            "parent_category": question_data['parent_category'],
            "child_category": question_data['child_category'],
            "content_focus": f"Processing error: {str(e)}"
        }
        return (idx, error_result)

def process_questions_sequential_batches(questions: List[Dict], business_context: str, 
                                       test_mode: bool = False, concurrent: int = 5) -> List[Dict]:
    """Process questions in sequential batches with concurrent processing within each batch."""
    
    if test_mode:
        questions = questions[:20]
        print(f"Running in test mode. Processing first {len(questions)} questions...")
    else:
        print(f"Processing {len(questions)} questions with {concurrent} concurrent connections per batch...")
    
    # Create smaller batches for sequential processing
    batch_size = 500  # Process 500 questions at a time
    all_results = []
    
    for batch_start in range(0, len(questions), batch_size):
        batch_end = min(batch_start + batch_size, len(questions))
        batch_questions = questions[batch_start:batch_end]
        batch_num = (batch_start // batch_size) + 1
        total_batches = (len(questions) + batch_size - 1) // batch_size
        
        print(f"\n[PROCESSING] Batch {batch_num}/{total_batches} ({len(batch_questions)} questions)")
        
        # Prepare arguments for concurrent processing within this batch
        args_list = [
            (batch_start + i, question_data, business_context)
            for i, question_data in enumerate(batch_questions)
        ]
        
        # Process this batch concurrently
        batch_results = []
        with ThreadPoolExecutor(max_workers=concurrent) as executor:
            futures = [executor.submit(process_single_question, args) for args in args_list]
            
            completed = 0
            for future in futures:
                try:
                    result = future.result()
                    batch_results.append(result)
                    completed += 1
                    
                    # Show progress every 50 questions
                    if completed % 50 == 0:
                        print(f"  [OK] {completed}/{len(batch_questions)} questions completed in batch {batch_num}")
                        
                except Exception as e:
                    print(f"  [ERROR] Error in batch {batch_num}: {e}")
        
        all_results.extend(batch_results)
        print(f"  [COMPLETE] Batch {batch_num} completed: {len(batch_results)} questions processed")
        
        # Small delay between batches to avoid overwhelming the server
        if batch_num < total_batches:
            time.sleep(2)
    
    # Sort results by original index to maintain order
    all_results.sort(key=lambda x: x[0])
    
    # Extract just the result dictionaries
    final_results = [result for _, result in all_results]
    
    return final_results

def save_results_to_csv(results: List[Dict], filename: str):
    """Save results to CSV in the requested format."""
    fieldnames = ["original_question", "parent_category", "child_category", "content_focus"]
    
    with open(filename, 'w', newline='', encoding='utf-8') as file:
        writer = csv.DictWriter(file, fieldnames=fieldnames)
        writer.writeheader()
        
        for result in results:
            csv_row = {}
            for field in fieldnames:
                value = result.get(field)
                csv_row[field] = value if value is not None else ""
            writer.writerow(csv_row)
    
    print(f"Results saved to {filename}")

def main():
    """Main function to run the AIQ Labs BOFU Content Generator."""
    parser = argparse.ArgumentParser(description="Generate BOFU content focus for already-categorized AIQ Labs questions")
    parser.add_argument("--test", action="store_true", help="Run in test mode with first 20 questions")
    parser.add_argument("--input", default="aiqlabs_categorized_questions.csv",
                       help="Input CSV file with already categorized questions")
    parser.add_argument("--context", default="AIQ Labs Context.txt", 
                       help="Text file with business context")
    parser.add_argument("--output", default="aiqlabs_bofu_content_focus.csv", 
                       help="Output CSV file")
    parser.add_argument("--concurrent", type=int, default=5, 
                       help="Number of concurrent connections per batch (default: 5)")
    
    args = parser.parse_args()
    
    print("=== AIQ Labs BOFU Content Focus Generator ===\n")
    
    # Check if input files exist
    for file_path in [args.input, args.context]:
        if not os.path.exists(file_path):
            print(f"Error: File '{file_path}' not found!")
            return
    
    try:
        # Read business context
        print("Loading business context...")
        business_context = read_business_context(args.context)
        
        # Read categorized questions
        print("Loading categorized questions...")
        questions = read_categorized_questions(args.input)
        print(f"Loaded {len(questions)} questions to process")
        
        # Process questions with sequential batch processing
        start_time = time.time()
        results = process_questions_sequential_batches(questions, business_context, args.test, args.concurrent)
        end_time = time.time()
        
        # Save results
        save_results_to_csv(results, args.output)
        
        # Print summary
        processing_time = end_time - start_time
        
        print(f"\n=== SUMMARY ===")
        print(f"Total questions processed: {len(results)}")
        print(f"Processing time: {processing_time/60:.1f} minutes")
        print(f"Average time per question: {processing_time/len(results):.2f} seconds")
        print(f"\nBOFU content focus saved to: {args.output}")
        
    except Exception as e:
        print(f"Error during processing: {e}")
        return

if __name__ == "__main__":
    main()