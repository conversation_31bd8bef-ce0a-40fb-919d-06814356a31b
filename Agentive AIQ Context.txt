AgentiveAIQ Platform: A Comprehensive Business Context Brief

1. Platform Overview: A Chatbot Agent System
AgentiveAIQ is a no-code platform for building, deploying, and managing specialized AI chatbot agent systems designed to drive specific business outcomes. The core of the platform is a sophisticated two-agent system that operates within highly customizable chat widgets and dedicated hosted environments.

The system is designed for deep brand integration, intelligent automation, and enhanced user engagement. It is not a system of autonomous agents but rather a powerful, goal-oriented chatbot framework with agentic capabilities confined to its conversational interfaces and backend automation.

Key Differentiators:

WYSIWYG Chat Widget Editor: Users can create fully customized floating and embedded chat widgets that perfectly match their website's branding without writing any code.

Dynamic Prompt Engineering: The platform dynamically assembles agent instructions from over 35 modular snippets, combining a core identity with one of nine specific goals, tone preferences, and operational rules for precise, context-aware conversations.

Two-Agent Architecture:

Main Chat Agent: The user-facing chatbot that engages visitors in real-time.

Assistant Agent: A background AI that analyzes completed conversations, extracts business intelligence, and sends detailed, personalized email summaries to the site owner.

Long-Term Memory Limitation: The system's long-term memory (recalling past conversations) is only available on hosted pages and courses where users are authenticated (e.g., via login). For anonymous visitors on a standard website widget, memory is session-based and is not retained after the conversation ends.

2. Core Components & Deployment Channels
A. Website Engagement Widgets (Floating & Embedded)
This is the platform's flagship feature. It allows businesses to deploy an AI agent on any website using a single line of code.

Total Visual Customization: A What-You-See-Is-What-You-Get (WYSIWYG) editor allows for deep customization of colors, logos, fonts, and styles to ensure the widget is a seamless part of the user's brand.

Simple Integration: Deployment is achieved by copying and pasting a single code snippet into a website's HTML.

B. Hosted AI Pages & AI-Powered Courses
Users can create standalone, brandable web pages and multi-step courses hosted by AgentiveAIQ.

Custom Branding & URL: Pages can be hosted on a unique URL with custom branding.

Gated Access & User Authentication: Access can be restricted with password protection, creating secure portals for clients, students, or internal teams.

Persistent Memory: When users are logged into a hosted page or course, the AI agent maintains long-term memory of all previous interactions with that specific user, providing a continuous and personalized experience.

AI Course Builder: A drag-and-drop interface allows for the creation of courses with rich media (video, audio, documents). The AI agent is automatically trained on all course materials to act as a 24/7 personal tutor for each student.

C. E-Commerce Integrations
One-click integrations provide the Main Chat Agent with real-time access to store data.

Supported Platforms: Shopify and WooCommerce.

Accessed Data: The agent can query product catalogs, inventory levels, order history, customer data, shipping zones, and coupon information to act as an intelligent sales and support assistant.

3. Intelligence Engine & Technical Architecture
The platform's intelligence is powered by a multi-layered architecture designed for accuracy, contextual understanding, and reliable automation.

A. Dual-Core Knowledge Base (RAG + Knowledge Graph)
AgentiveAIQ combines two advanced technologies to form the agent's "brain," ensuring responses are both fast and contextually aware.

Retrieval-Augmented Generation (RAG): Functions like a hyper-efficient search engine, pulling precise, factual snippets directly from the source knowledge base (uploaded documents, scraped websites) to answer direct questions instantly.

Knowledge Graph: Creates a structured, interconnected "mind map" of the information. This allows the AI to understand complex relationships between different concepts, enabling it to answer nuanced, multi-part questions that simple retrieval systems cannot.

B. Graph-Based Long-Term Memory
For authenticated users on hosted pages and courses, long-term memory is managed through a dedicated knowledge graph. This sophisticated approach allows the agent to build a detailed, relational understanding of a user's entire interaction history, leading to highly accurate and personalized follow-up conversations.

C. Agentic Flows & MCP Tools
The agents don't just talk; they execute tasks. This is achieved through a system of agentic flows and modular tools.

Agentic Flows: Pre-defined, goal-oriented sequences of actions the Main Chat Agent can take. For example, a "Lead Generation" flow involves identifying a user's need, providing information, and then triggering a tool to capture and send their contact details.

MCP (Modular Command Protocol) Tools: These are specific, single-purpose functions the agent can call upon. Examples include get_product_info from a Shopify store, send_lead_email to the site owner, or triggering a webhook to an external CRM.

D. Fact Validation Layer
To ensure reliability and eliminate "hallucinations," a final validation step is performed before any response is sent to the user. The agent's proposed answer is cross-referenced against the original source information retrieved from the RAG system. If the confidence score is low or a factual discrepancy is found, the answer is automatically re-generated, ensuring the user receives only accurate, verified information.

4. The Two-Agent System Explained
A. The Main Chat Agent
This is the primary, user-facing AI responsible for conducting conversations within the chat widget or hosted page.

Task: To engage users in real-time based on a pre-selected goal (e.g., Sales, Support).

Operation: It follows instructions from the dynamic prompt system, uses the knowledge base to answer questions, and can execute simple automated actions like sending a lead notification via email or webhook based on user settings.

Knowledge: Relies on a knowledge base created by scraping websites or uploading documents (PDF, DOCX, etc.).

B. The Assistant Agent
This AI works in the background and does not interact with website visitors. Its sole purpose is to provide business intelligence to the platform user (the site owner).

Task: After a conversation between the Main Agent and a visitor is complete, the Assistant Agent reads and analyzes the entire transcript.

Operation: It performs sentiment analysis, identifies issues, flags opportunities (e.g., upsells, churn risks), and assesses the overall value of the conversation.

Output: Based on its analysis and the conversation's goal, it compiles and sends a structured, personalized email summary to the site owner, highlighting actionable insights.

5. The 9 Pre-Built Agent Goals (Main & Assistant Agent Tasks)
Each goal configures the behavior of both the Main Chat Agent and the Assistant Agent.

1. E-Commerce
Main Agent's Task: Act as a customer-focused shopping assistant. Use e-commerce integrations (Shopify/WooCommerce) to answer product questions, check stock, provide personalized recommendations, and assist with order-related queries to guide customers to a purchase.

Assistant Agent's Analysis: Analyzes chat transcripts to identify product interest, customer objections, reasons for cart abandonment, and overall sentiment about the shopping experience. It triggers emails for high-value customers with concerns, technical checkout issues, or valuable product feedback.

2. Customer Support
Main Agent's Task: Resolve customer issues efficiently. Use the knowledge base to answer questions, provide step-by-step guidance, and escalate complex issues to human support via email or webhooks when necessary.

Assistant Agent's Analysis: Performs a root cause analysis on each support interaction to identify product bugs, process gaps, or documentation needs. It triggers emails for unresolved issues, highly frustrated customers, or feedback that could prevent future support tickets.

3. Sales & Lead Generation
Main Agent's Task: Identify and qualify potential sales leads. It engages prospects, understands their needs and urgency, aligns their challenges with the business's solutions, and naturally collects contact information to pass on to the sales team.

Assistant Agent's Analysis: Qualifies the lead based on budget, authority, need, and timeline (BANT). It identifies pain points, buying signals, and competitive mentions. It triggers emails for "hot leads" who express immediate intent, frustration with a competitor, or have a time-sensitive opportunity.

4. Real Estate
Main Agent's Task: Assist prospective home buyers and sellers. It identifies their intent, assesses their urgency (e.g., relocation, life events), and evaluates their readiness (e.g., pre-approval status). Its goal is to connect serious, qualified prospects with licensed agents.

Assistant Agent's Analysis: Analyzes client conversations for specific property preferences, motivation levels, key concerns (market conditions, financing), and relationship-building opportunities (personal details). It triggers emails for high-value clients ready to make an offer or those expressing significant urgency.

5. Finance
Main Agent's Task: Act as a first point of contact for financial services (e.g., mortgage, auto, personal loans). It helps prospects understand their options, assesses their financial situation and readiness, and connects qualified candidates with financial specialists.

Assistant Agent's Analysis: Analyzes chats to determine a client's financial goals, product interest, financial literacy level, and specific concerns. It triggers emails for high-net-worth individuals, clients experiencing a major life event, or those raising compliance concerns.

6. Education
Main Agent's Task: Serve as a supportive teaching assistant for students. It uses the provided course materials in the knowledge base to answer questions, explain concepts, and reinforce learning. For complex or grade-related questions, it escalates to a human instructor.

Assistant Agent's Analysis: Monitors student interactions to identify concept comprehension issues, struggle patterns, and personalization opportunities. It triggers emails when a student is falling significantly behind, showing exceptional progress, or is blocked by a learning barrier.

7. HR & Internal Support
Main Agent's Task: Act as a confidential, 24/7 HR resource for employees. It uses the internal knowledge base to answer questions about company policies, benefits, and procedures. It is trained to immediately escalate sensitive issues to human HR.

Assistant Agent's Analysis: Reviews employee interactions with strict confidentiality. It looks for common questions that indicate policy confusion, sentiment on new policies, and indicators of organizational health (e.g., morale, communication gaps). It triggers emails when it detects policy confusion, significant dissatisfaction, or potential compliance risks.

8. Training & Onboarding
Main Agent's Task: Support new hires during their onboarding. It provides information from training manuals, explains processes, and helps new employees navigate their first few weeks. Its goal is to build confidence and escalate to trainers when hands-on help is needed.

Assistant Agent's Analysis: Identifies knowledge gaps and assesses the effectiveness of training materials by analyzing new hire questions. It flags learners who are struggling, materials that are outdated, or technical issues blocking progress, alerting trainers via email.

9. Custom
Main Agent's Task: A flexible agent whose behavior is defined by a custom goal provided by the user. It still adheres to core principles like using the knowledge base for accuracy but adapts its conversational style and objectives to the user-defined instructions.

Assistant Agent's Analysis: Adapts its analysis based on the custom goal. It looks for industry-specific insights, customer journey friction points, and business-specific opportunities as defined by the user, triggering emails for high-value events relevant to that custom context.

6. Pricing & Plans
AgentiveAIQ offers three self-serve plans. A 14-day free Pro trial is available.

Base Plan
Cost: $39/month

Includes:

2 Chat Agents

2,500 Messages/month

100,000 Character knowledge base

Includes "Powered by AgentiveAIQ" branding

Pro Plan (Most Popular)
Cost: $129/month

Includes:

8 Chat Agents

25,000 Messages/month

1,000,000 Character knowledge base

5 Secure hosted pages

No AgentiveAIQ branding

Advanced Features: Smart Triggers, AI Courses, Long-term Memory, Assistant Agent (Sentiment Analysis), Webhook Notifications, Shopify & WooCommerce integrations.

Agency Plan
Cost: $449/month

Includes:

50 Chat Agents

100,000 Messages/month

10,000,000 Character knowledge base

50 Hosted Pages

All Pro features

Agency Exclusives: Custom client-facing branding, dedicated account manager, phone support.