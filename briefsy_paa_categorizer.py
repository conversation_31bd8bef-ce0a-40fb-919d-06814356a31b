#!/usr/bin/env python3
"""
Briefsy PAA Question Categorizer

This script categorizes People Also Ask (PAA) questions for Briefsy blog content creation.
It focuses on bottom-of-the-funnel content that aligns with <PERSON>riefsy's business context.
Uses VLLM endpoint with concurrent processing for faster results.

Output format: original_question,parent_category,child_category,content_focus
"""

import csv
import json
import argparse
import requests
import time
import os
import asyncio
import aiohttp
from typing import List, Dict, Tuple
from concurrent.futures import ThreadPoolExecutor
import threading

# Configuration for VLLM API
VLLM_ENDPOINT = "http://**************:4100/v1/chat/completions"
VLLM_MODEL = "Qwen/Qwen3-4B-Instruct-2507"
VLLM_API_KEY = "this-is-my-key"
MAX_CONCURRENT = 3  # Reduced from 10 to avoid overwhelming the server

def read_paa_questions(filename: str) -> List[str]:
    """Read PAA questions from CSV file."""
    questions = []
    with open(filename, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            # Handle different possible column names
            if 'PAA Title' in row:
                questions.append(row['PAA Title'])
            elif 'Questions' in row:
                questions.append(row['Questions'])
            elif 'original_question' in row:
                questions.append(row['original_question'])
            elif 'PAA_Question' in row:
                questions.append(row['PAA_Question'])
            else:
                # Use first column value
                first_column = next(iter(row.values()))
                questions.append(first_column)
    return questions

def read_blog_categories(filename: str) -> List[Tuple[str, str]]:
    """Read blog categories from CSV file - handles both formats."""
    categories = []
    with open(filename, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            # Handle both formats: "Parent Category" or "parent_category"
            if 'Parent Category' in row:
                parent = row['Parent Category'].strip()
                child = row['Child Category'].strip()
            elif 'parent_category' in row:
                parent = row['parent_category'].strip()
                child = row['child_category'].strip()
            else:
                # Fallback to first two columns
                values = list(row.values())
                parent = values[0].strip() if len(values) > 0 else ""
                child = values[1].strip() if len(values) > 1 else ""
            categories.append((parent, child))
    return categories

def read_business_context(filename: str) -> str:
    """Read business context from text file."""
    with open(filename, 'r', encoding='utf-8') as file:
        return file.read()

def get_categories_context(valid_categories: List[Tuple[str, str]]) -> str:
    """Return formatted category pairs for the AI to choose from."""
    context = "You MUST choose from one of these EXACT parent-child category pairs:\n\n"
    
    for i, (parent, child) in enumerate(valid_categories, 1):
        context += f"{i}. Parent: \"{parent}\" | Child: \"{child}\"\n"
    
    context += "\nYou are REQUIRED to select one of these exact pairs. Do not create new categories or modify the names."
    return context

def query_vllm_api(prompt: str, max_tokens: int = 600) -> str:
    """Query the VLLM API."""
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {VLLM_API_KEY}"
    }
    
    data = {
        "model": VLLM_MODEL,
        "messages": [
            {"role": "user", "content": prompt}
        ],
        "temperature": 0.3,
        "max_tokens": max_tokens
    }
    
    max_retries = 3
    for attempt in range(max_retries):
        try:
            response = requests.post(VLLM_ENDPOINT, headers=headers, json=data, timeout=30)
            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content']
            else:
                print(f"Error: API returned status code {response.status_code}")
                if attempt < max_retries - 1:
                    time.sleep(2)  # Wait before retry
                    continue
                return "Error processing request"
        except requests.exceptions.Timeout:
            print(f"Timeout on attempt {attempt + 1}/{max_retries}")
            if attempt < max_retries - 1:
                time.sleep(5)  # Wait longer before retry on timeout
                continue
            return "Error: Request timeout"
        except Exception as e:
            print(f"Error connecting to VLLM (attempt {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                time.sleep(3)  # Wait before retry
                continue
            return "Error connecting to VLLM"

def is_bottom_funnel_question(question: str, business_context: str) -> bool:
    """Determine if a question is bottom-of-the-funnel (implementation/solution focused)."""
    question_lower = question.lower()
    
    # Bottom-funnel indicators
    bottom_funnel_keywords = [
        'how to', 'how can', 'best way to', 'implement', 'setup', 'configure',
        'integrate', 'automate', 'cost', 'pricing', 'roi', 'benefits',
        'solution', 'tool', 'platform', 'software', 'system',
        'comparison', 'vs', 'versus', 'alternative', 'replace',
        'workflow', 'process', 'efficiency', 'productivity', 'personalize',
        'curate', 'newsletter', 'content', 'subscription'
    ]
    
    # Top-funnel indicators (to avoid)
    top_funnel_keywords = [
        'what is', 'what are', 'definition', 'meaning', 'explain',
        'history of', 'who invented', 'when was', 'father of',
        'types of', 'examples of', 'introduction to'
    ]
    
    # Check for bottom-funnel indicators
    has_bottom_funnel = any(keyword in question_lower for keyword in bottom_funnel_keywords)
    
    # Check for top-funnel indicators
    has_top_funnel = any(keyword in question_lower for keyword in top_funnel_keywords)
    
    # Prefer bottom-funnel questions, but don't completely exclude top-funnel if they're business-relevant
    return has_bottom_funnel or not has_top_funnel

def categorize_question(question: str, valid_categories: List[Tuple[str, str]], business_context: str) -> Dict:
    """Categorize a single PAA question for Briefsy blog content."""
    
    prompt = f"""
You are an AI assistant helping Briefsy categorize People Also Ask (PAA) questions for blog content creation.

BUSINESS CONTEXT:
{business_context}

CRITICAL INSTRUCTIONS:
You MUST select from these EXACT parent-child category pairs. DO NOT create new categories or modify names.

{get_categories_context(valid_categories)}

FOCUS: We want bottom-of-the-funnel content that helps prospects who are already interested in newsletter solutions, content curation, or personalization and are looking for implementation guidance, comparisons, or specific solutions.

Question: "{question}"

TASK:
1. Determine if this question is relevant to Briefsy's business and suitable for a blog article
2. If relevant, categorize it into ONE of the exact parent-child category pairs listed above
3. If NOT relevant to Briefsy's business, set both categories to null
4. Provide a content focus paragraph explaining how this connects to Briefsy's solutions

You MUST respond with this EXACT JSON format:
{{
  "parent_category": "Copy exact parent name from list above" OR null if not relevant,
  "child_category": "Copy exact child name from list above" OR null if not relevant,
  "content_focus": "A paragraph explaining how this question connects to Briefsy's business context and how it can be used for a blog post. Focus on specific Briefsy solutions, benefits, and implementation approaches. If not relevant, state 'Not relevant to Briefsy business context.'"
}}

REMEMBER:
- Only use the exact category names from the numbered list above
- Focus on questions that help prospects understand newsletter curation, personalization, or content solutions
- If the question is too general or not relevant to Briefsy's AI-powered newsletter platform, use null for both categories
- The content_focus should be a substantial paragraph (3-4 sentences) that clearly connects the question to Briefsy's specific capabilities
"""

    response = query_vllm_api(prompt, max_tokens=600)
    
    # Try to extract JSON from response
    try:
        # Find the first { and last } to extract JSON
        start = response.find('{')
        end = response.rfind('}') + 1
        if start != -1 and end > start:
            json_str = response[start:end]
            result = json.loads(json_str)
            
            # Validate the response
            parent = result.get("parent_category")
            child = result.get("child_category")
            
            # If both are null, the AI determined it's not relevant
            if parent is None and child is None:
                return result
            
            # If we have categories, validate them against our list
            if parent and child:
                parent = parent.strip()
                child = child.strip()
                valid_set = set(valid_categories)
                if (parent, child) in valid_set:
                    return result
            
            # If categories are invalid, mark as not relevant
            return {
                "parent_category": None,
                "child_category": None,
                "content_focus": "Not relevant to Briefsy business context."
            }
        else:
            return {
                "parent_category": None,
                "child_category": None,
                "content_focus": "Could not parse AI response."
            }
    except json.JSONDecodeError:
        return {
            "parent_category": None,
            "child_category": None,
            "content_focus": "JSON parsing error in AI response."
        }

def process_question_batch(questions_batch: List[Tuple[int, str]], valid_categories: List[Tuple[str, str]],
                          business_context: str, batch_id: int = 0) -> List[Tuple[int, Dict]]:
    """Process a batch of questions and return results with original indices."""
    results = []
    batch_size = len(questions_batch)

    for i, (idx, question) in enumerate(questions_batch):
        try:
            result = categorize_question(question, valid_categories, business_context)
            result["original_question"] = question
            results.append((idx, result))

            # Log progress every 100 questions within batch
            if (i + 1) % 100 == 0:
                print(f"  Batch {batch_id}: {i + 1}/{batch_size} questions processed")

        except Exception as e:
            print(f"Error processing question {idx} in batch {batch_id}: {e}")
            error_result = {
                "original_question": question,
                "parent_category": None,
                "child_category": None,
                "content_focus": f"Processing error: {str(e)}"
            }
            results.append((idx, error_result))

    print(f"  Batch {batch_id}: Completed all {batch_size} questions")
    return results

def process_questions_concurrent(questions: List[str], valid_categories: List[Tuple[str, str]], 
                               business_context: str, test_mode: bool = False) -> List[Dict]:
    """Process questions using concurrent processing."""
    
    # Limit to first 20 questions in test mode
    if test_mode:
        questions = questions[:20]
        print(f"Running in test mode. Processing first {len(questions)} questions...")
    else:
        print(f"Processing {len(questions)} questions with {MAX_CONCURRENT} concurrent connections...")
    
    # Create batches for concurrent processing
    batch_size = max(1, len(questions) // MAX_CONCURRENT)
    batches = []

    for i in range(0, len(questions), batch_size):
        batch = [(i + j, questions[i + j]) for j in range(min(batch_size, len(questions) - i))]
        batches.append(batch)

    print(f"Total questions: {len(questions)}")
    print(f"Max concurrent: {MAX_CONCURRENT}")
    print(f"Calculated batch size: {batch_size}")
    print(f"Created {len(batches)} batches")
    for i, batch in enumerate(batches):
        print(f"  Batch {i+1}: {len(batch)} questions")
    
    # Process batches concurrently
    all_results = []
    
    try:
        with ThreadPoolExecutor(max_workers=MAX_CONCURRENT) as executor:
            # Submit all batches
            future_to_batch = {
                executor.submit(process_question_batch, batch, valid_categories, business_context, i+1): (batch, i+1)
                for i, batch in enumerate(batches)
            }
            
            # Collect results as they complete
            completed_batches = 0
            for future in future_to_batch:
                try:
                    batch_results = future.result()
                    all_results.extend(batch_results)
                    completed_batches += 1
                    batch_info = future_to_batch[future]
                    batch_id = batch_info[1]
                    print(f"✅ Completed batch {batch_id}/{len(batches)} ({completed_batches} total completed)")
                except Exception as e:
                    batch_info = future_to_batch[future]
                    batch_id = batch_info[1]
                    print(f"❌ Error processing batch {batch_id}: {e}")
    
    except KeyboardInterrupt:
        print(f"\nProcess interrupted by user. Processed {len(all_results)} questions.")
        
    # Sort results by original index to maintain order
    all_results.sort(key=lambda x: x[0])
    
    # Extract just the result dictionaries
    final_results = [result for _, result in all_results]
    
    return final_results

def save_results_to_csv(results: List[Dict], filename: str):
    """Save results to CSV in the requested format."""
    fieldnames = ["original_question", "parent_category", "child_category", "content_focus"]
    
    with open(filename, 'w', newline='', encoding='utf-8') as file:
        writer = csv.DictWriter(file, fieldnames=fieldnames)
        writer.writeheader()
        
        for result in results:
            # Convert None values to empty strings for CSV
            csv_row = {}
            for field in fieldnames:
                value = result.get(field)
                csv_row[field] = value if value is not None else ""
            writer.writerow(csv_row)
    
    print(f"Results saved to {filename}")

def main():
    """Main function to run the Briefsy PAA categorizer."""
    parser = argparse.ArgumentParser(description="Categorize PAA questions for Briefsy blog content")
    parser.add_argument("--test", action="store_true", help="Run in test mode with first 20 questions")
    parser.add_argument("--questions", default="Briefsy PAA - Newsletter Selection Guides_PAA (4).csv",
                       help="Input CSV file with PAA questions")
    parser.add_argument("--categories", default="Briefsy Categories - Sheet1.csv", 
                       help="CSV file with blog categories")
    parser.add_argument("--context", default="Briefsy_context.txt", 
                       help="Text file with business context")
    parser.add_argument("--output", default="briefsy_categorized_questions.csv",
                       help="Output CSV file")
    parser.add_argument("--concurrent", type=int, default=3,
                       help="Number of concurrent connections (default: 3)")
    
    args = parser.parse_args()
    
    print("=== Briefsy PAA Question Categorizer ===\n")
    
    # Check if input files exist
    for file_path in [args.questions, args.categories, args.context]:
        if not os.path.exists(file_path):
            print(f"Error: File '{file_path}' not found!")
            return
    
    try:
        # Read business context
        print("Loading business context...")
        business_context = read_business_context(args.context)
        
        # Read valid categories
        print("Loading blog categories...")
        valid_categories = read_blog_categories(args.categories)
        print(f"Loaded {len(valid_categories)} valid category pairs")
        
        # Read questions
        print("Loading PAA questions...")
        questions = read_paa_questions(args.questions)
        print(f"Loaded {len(questions)} questions to categorize")
        
        # Process questions with concurrent processing
        global MAX_CONCURRENT
        MAX_CONCURRENT = args.concurrent
        print(f"Using {MAX_CONCURRENT} concurrent connections")

        start_time = time.time()
        results = process_questions_concurrent(questions, valid_categories, business_context, args.test)
        end_time = time.time()
        
        # Save results
        save_results_to_csv(results, args.output)
        
        # Print summary
        relevant_count = sum(1 for r in results if r.get('parent_category') is not None)
        processing_time = end_time - start_time
        
        print(f"\n=== SUMMARY ===")
        print(f"Total questions processed: {len(results)}")
        print(f"Relevant for blog content: {relevant_count} ({relevant_count/len(results)*100:.1f}%)")
        print(f"Not relevant: {len(results) - relevant_count} ({(len(results) - relevant_count)/len(results)*100:.1f}%)")
        print(f"Processing time: {processing_time:.1f} seconds")
        print(f"Average time per question: {processing_time/len(results):.2f} seconds")
        print(f"\nOutput saved to: {args.output}")
        
    except Exception as e:
        print(f"Error during processing: {e}")
        return

if __name__ == "__main__":
    main()
