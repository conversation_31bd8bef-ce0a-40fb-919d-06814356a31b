# PAA Question Categorizer for AgentiveAI

This tool processes People Also Ask (PAA) questions and categorizes them based on AgentiveAI's business context and blog categories. It uses your local LM Studio setup with the Mistral-7B-Instruct-v0.3 model to filter and categorize questions that are relevant for blog content creation.

## Features

- Filters PAA questions to identify those relevant to AgentiveAI's business
- Categorizes relevant questions into appropriate parent and child blog categories
- Provides confidence scores for categorization decisions
- Suggests content focus for each relevant question
- Supports test mode for validation before full processing
- Processes all 11,472 PAA questions using your local LLM

## Requirements

- Python 3.6+
- Local LM Studio setup with Mistral-7B-Instruct-v0.3 model
- Requests library (installed via requirements.txt)

## Installation

1. Ensure you have Python 3.6+ installed
2. Install the required dependencies:
   ```
   pip install -r requirements.txt
   ```

## Usage

### Test Mode
Process only the first 5 questions to verify the system is working correctly:
```
python paa_categorizer.py --test
```

### Full Processing
Process all PAA questions:
```
python paa_categorizer.py
```

### Custom Input/Output Files
Specify custom input and output files:
```
python paa_categorizer.py --input your_paa_questions.csv --output categorized_results.csv
```

## Output Format

The script generates a CSV file with the following columns:
- `original_question`: The original PAA question
- `relevant`: Boolean indicating if the question is relevant to AgentiveAI
- `parent_category`: The assigned parent blog category (if relevant)
- `child_category`: The assigned child blog category (if relevant)
- `confidence_score`: Confidence score for the categorization (1-10)
- `content_focus`: Suggested approach for creating a blog article on this topic

## How It Works

1. The script reads PAA questions from the input CSV file
2. For each question, it queries your local LM Studio API with:
   - AgentiveAI business context
   - Blog category information
   - The specific question to categorize
3. The LLM evaluates the question and returns:
   - Relevance to AgentiveAI's business
   - Appropriate blog category (if relevant)
   - Confidence score
   - Content focus suggestion
4. Results are saved to a new CSV file

## Troubleshooting

If you encounter connection issues:
1. Ensure LM Studio is running and the Mistral-7B-Instruct-v0.3 model is loaded
2. Verify the API URL (http://*************:1234/v1/chat/completions) is correct
3. Check that no API key is required for your setup

## Performance Notes

- Processing all 11,472 questions will take several hours due to the local LLM processing time
- The script includes a small delay between requests to avoid overwhelming the API
- Progress is displayed during processing
