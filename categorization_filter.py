#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to filter PAA questions based on exact category matches.

This script reads the output from the categorization agent and separates:
1. Rows that exactly match valid parent-child category combinations
2. Rows that don't match and need reassignment

Input files:
- output - Sheet1.csv: Contains categorized PAA questions
- categories - Sheet1.csv: Contains valid parent-child category combinations

Output files:
- matched_categories.csv: Questions with exact category matches
- unmatched_categories.csv: Questions that need reassignment
"""

import pandas as pd
import os
from typing import Set, Tuple

def load_valid_categories(categories_file: str) -> Set[Tuple[str, str]]:
    """
    Load valid parent-child category combinations from the categories file.
    
    Args:
        categories_file: Path to the categories CSV file
        
    Returns:
        Set of (parent_category, child_category) tuples
    """
    try:
        categories_df = pd.read_csv(categories_file)
        print(f"Loaded {len(categories_df)} valid category combinations")
        
        # Create set of (parent, child) tuples
        valid_combinations = set()
        for _, row in categories_df.iterrows():
            parent = row['parent_cats'].strip() if pd.notna(row['parent_cats']) else ''
            child = row['sub_cats'].strip() if pd.notna(row['sub_cats']) else ''
            valid_combinations.add((parent, child))
        
        print(f"Valid parent categories: {len(set(combo[0] for combo in valid_combinations))}")
        print(f"Valid child categories: {len(set(combo[1] for combo in valid_combinations))}")
        
        return valid_combinations
        
    except Exception as e:
        print(f"Error loading categories file: {e}")
        raise

def filter_categorized_questions(output_file: str, valid_combinations: Set[Tuple[str, str]]) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Filter the categorized questions into matched and unmatched groups.
    
    Args:
        output_file: Path to the output CSV file with categorized questions
        valid_combinations: Set of valid (parent, child) category combinations
        
    Returns:
        Tuple of (matched_df, unmatched_df)
    """
    try:
        output_df = pd.read_csv(output_file)
        print(f"Loaded {len(output_df)} categorized questions")
        
        # Clean up the data - strip whitespace and handle NaN values
        output_df['parent_category'] = output_df['parent_category'].fillna('').astype(str).str.strip()
        output_df['child_category'] = output_df['child_category'].fillna('').astype(str).str.strip()
        
        # Create boolean mask for exact matches
        matches = []
        for _, row in output_df.iterrows():
            parent = row['parent_category']
            child = row['child_category']
            combination = (parent, child)
            matches.append(combination in valid_combinations)
        
        # Split into matched and unmatched dataframes
        matched_df = output_df[matches].copy()
        unmatched_df = output_df[~pd.Series(matches)].copy()
        
        print(f"Matched questions: {len(matched_df)}")
        print(f"Unmatched questions: {len(unmatched_df)}")
        
        return matched_df, unmatched_df
        
    except Exception as e:
        print(f"Error processing output file: {e}")
        raise

def analyze_unmatched_categories(unmatched_df: pd.DataFrame):
    """
    Analyze the unmatched categories to understand what went wrong.
    
    Args:
        unmatched_df: DataFrame containing unmatched questions
    """
    print("\n=== ANALYSIS OF UNMATCHED CATEGORIES ===")
    
    # Count unique parent categories in unmatched data
    unique_parents = unmatched_df['parent_category'].value_counts()
    print(f"\nUnique parent categories in unmatched data ({len(unique_parents)}):")
    for parent, count in unique_parents.head(10).items():
        print(f"  '{parent}': {count} questions")
    
    # Count unique child categories in unmatched data
    unique_children = unmatched_df['child_category'].value_counts()
    print(f"\nUnique child categories in unmatched data ({len(unique_children)}):")
    for child, count in unique_children.head(10).items():
        print(f"  '{child}': {count} questions")
    
    # Count blank/empty categories
    blank_parents = len(unmatched_df[unmatched_df['parent_category'] == ''])
    blank_children = len(unmatched_df[unmatched_df['child_category'] == ''])
    print(f"\nBlank categories:")
    print(f"  Blank parent categories: {blank_parents}")
    print(f"  Blank child categories: {blank_children}")

def main():
    """Main function to execute the categorization filtering."""
    
    # File paths
    categories_file = "categories - Sheet1.csv"
    output_file = "output - Sheet1.csv"
    matched_output = "matched_categories.csv"
    unmatched_output = "unmatched_categories.csv"
    
    print("=== PAA QUESTION CATEGORIZATION FILTER ===\n")
    
    # Check if input files exist
    if not os.path.exists(categories_file):
        print(f"Error: Categories file '{categories_file}' not found!")
        return
    
    if not os.path.exists(output_file):
        print(f"Error: Output file '{output_file}' not found!")
        return
    
    try:
        # Load valid category combinations
        print("Loading valid category combinations...")
        valid_combinations = load_valid_categories(categories_file)
        
        # Filter the categorized questions
        print("\nFiltering categorized questions...")
        matched_df, unmatched_df = filter_categorized_questions(output_file, valid_combinations)
        
        # Save the results
        print(f"\nSaving matched questions to '{matched_output}'...")
        matched_df.to_csv(matched_output, index=False)
        
        print(f"Saving unmatched questions to '{unmatched_output}'...")
        unmatched_df.to_csv(unmatched_output, index=False)
        
        # Analyze unmatched categories
        if len(unmatched_df) > 0:
            analyze_unmatched_categories(unmatched_df)
        
        print(f"\n=== SUMMARY ===")
        print(f"Total questions processed: {len(matched_df) + len(unmatched_df)}")
        print(f"Perfectly matched: {len(matched_df)} ({len(matched_df)/(len(matched_df) + len(unmatched_df))*100:.1f}%)")
        print(f"Need reassignment: {len(unmatched_df)} ({len(unmatched_df)/(len(matched_df) + len(unmatched_df))*100:.1f}%)")
        print(f"\nFiles created:")
        print(f"  - {matched_output}")
        print(f"  - {unmatched_output}")
        
    except Exception as e:
        print(f"Error during processing: {e}")
        return

if __name__ == "__main__":
    main()
