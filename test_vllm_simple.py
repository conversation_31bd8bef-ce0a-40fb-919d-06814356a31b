#!/usr/bin/env python3
"""
Simple VLLM connection test
"""

import requests
import json

# Configuration
VLLM_ENDPOINT = "http://192.222.59.118:4100/v1/chat/completions"
VLLM_MODEL = "Qwen/Qwen3-4B-Instruct-2507"
VLLM_API_KEY = "this-is-my-key"

def test_vllm_connection():
    """Test basic connection to VLLM server."""
    print(f"Testing connection to: {VLLM_ENDPOINT}")
    print(f"Using model: {VLLM_MODEL}")
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {VLLM_API_KEY}"
    }
    
    data = {
        "model": VLLM_MODEL,
        "messages": [
            {"role": "user", "content": "Hello, are you working? Please respond with 'Yes, VLLM is working'."}
        ],
        "temperature": 0.3,
        "max_tokens": 50
    }
    
    try:
        print("Sending request...")
        response = requests.post(VLLM_ENDPOINT, headers=headers, json=data, timeout=10)
        
        print(f"Status code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            content = result['choices'][0]['message']['content']
            print(f"✅ SUCCESS: {content}")
            return True
        else:
            print(f"❌ ERROR: Status {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectTimeout:
        print("❌ CONNECTION TIMEOUT: Server is not responding")
        return False
    except requests.exceptions.ConnectionError as e:
        print(f"❌ CONNECTION ERROR: {e}")
        return False
    except Exception as e:
        print(f"❌ UNEXPECTED ERROR: {e}")
        return False

if __name__ == "__main__":
    test_vllm_connection()
