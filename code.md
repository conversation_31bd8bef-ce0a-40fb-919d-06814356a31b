import React, { useState } from 'react';
import { Download, Shuffle, Settings } from 'lucide-react';

const BOFUTitleGenerator = () => {
  const [targetCount, setTargetCount] = useState(5000);
  const [generatedTitles, setGeneratedTitles] = useState([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [showSettings, setShowSettings] = useState(false);

  // Enhanced qualifiers with pain point, ROI, and comparison focus
  const qualifiers = [
    // Original high-intent
    "Best", "Top", "Leading", "#1", "Hire", "Find", 
    "Top-Rated", "Fastest", "Most Reliable", "Expert", "Professional",
    "Affordable", "Enterprise", "Custom", "Trusted", "Premier",
    // Pain point & solution focused
    "Fix for", "Solve", "Alternative to", "Replacement for", "Upgrade from",
    // Cost & ROI focused
    "Cost of", "Pricing", "ROI of", "Value of",
    // Comparison & decision focused
    "Review", "Guide to Choosing", "Comparison"
  ];

  // Enhanced service types with AI Agent focus and problem/solution language
  const serviceTypes = [
    // Original
    "AI Development Company", "AI Agency", "AI Automation Agency",
    "Custom AI Solutions", "AI Workflow Automation", "AI Integration Services",
    "Multi-Agent Systems", "Custom Software Development", "Business Automation Solutions",
    "SaaS Development Company",
    
    // AI Agent-specific
    "AI Agent Development", "Custom AI Agent Builders", "Autonomous AI Agent Solutions",
    "AI Sales Agent Development", "AI Customer Support Agents", "AI Research Agents",
    
    // Problem/solution focused
    "SaaS Subscription Consolidation", "Workflow Integration Solutions",
    "Business Process Automation", "Custom Internal Tool Development",
    
    // Strategic & niche services
    "AI SEO Systems", "LLM Ranking Solutions", "AI Influencer Creation",
    "Generative AI Integration", "AI-Powered Business Intelligence",
    "AI Chatbot Development", "Voice AI Solutions", "AI-Powered Software",
    "AI Content Generation", "Lead Generation AI", "AI CRM Integration",
    "AI Marketing Automation", "AI Sales Tools", "Intelligent Workflow Systems",
    "AI Dashboard Development", "RAG System Development", "LangGraph Developers"
  ];

  // Expanded industries with digital-first and high-value professional services
  const industries = [
    // Service Industries
    "Landscaping", "Plumbing", "HVAC", "Electrical", "Snow Removal",
    "Construction", "Home Builders", "Roofing", "Cleaning Services",
    "Pest Control", "Pool Maintenance", "Tree Services", "Locksmith",
    
    // Hospitality & Food
    "Hotels", "Restaurants", "Cafes", "Bars", "Catering", "Food Delivery",
    "Event Planning", "Wedding Planning", "Travel Agencies", "Event Management Companies",
    
    // Professional Services
    "Law Firms", "Legal Services", "Accounting Firms", "Consulting",
    "Marketing Agencies", "PR Firms", "Recruitment", "HR Services",
    "Financial Advisors", "Insurance Agencies", "Tax Preparation",
    "Management Consulting", "Architecture Firms", "Engineering Firms",
    
    // Healthcare & Wellness
    "Medical Practices", "Dental Clinics", "Veterinary Clinics",
    "Physical Therapy", "Mental Health", "Chiropractic", "Pharmacies",
    "Home Healthcare", "Medical Spas", "Fitness Centers", "Yoga Studios",
    
    // Real Estate & Property
    "Real Estate Agencies", "Property Management", "Commercial Real Estate",
    "Mortgage Brokers", "Title Companies", "Real Estate Investment",
    
    // Automotive
    "Auto Dealers", "Car Dealerships", "Auto Repair", "Auto Body Shops",
    "Car Washes", "Auto Parts", "RV Dealers", "Motorcycle Dealers",
    
    // Retail & E-commerce
    "E-commerce Stores", "Retail Shops", "Online Retailers", "Fashion Boutiques",
    "Jewelry Stores", "Furniture Stores", "Home Decor", "Electronics Stores",
    
    // Education
    "Schools", "Universities", "Online Education", "Tutoring Services",
    "Training Centers", "Language Schools", "Daycare Centers", "E-learning Platforms",
    
    // Finance
    "Banks", "Credit Unions", "Fintech", "Investment Firms", "Wealth Management",
    "Payment Processing", "Lending Companies", "Private Equity Firms", "Venture Capital",
    
    // Manufacturing & Distribution
    "Manufacturing", "Wholesale", "Distribution", "Logistics", "Supply Chain",
    "Warehousing", "Industrial Services",
    
    // Digital-First & Tech
    "SaaS Companies", "Digital Marketing Agencies", "Media & Publishing",
    "Software Development Houses", "App Developers", "Tech Startups",
    
    // Operations-Heavy
    "Franchise Businesses", "Non-Profits"
  ];

  // Enhanced specific products with better naming
  const specificProducts = {
    "Lead Generation": [
      "AI Lead Generation", "Autonomous Lead Qualification Agents", 
      "Lead Scoring AI", "Lead Nurturing Automation",
      "AI Sales Development Rep (SDR) Automation", "Inbound Lead Routing Systems"
    ],
    "Customer Service": [
      "AI Chatbots", "Voice AI Agents", "Support Ticket Automation", 
      "24/7 AI Support", "AI Customer Support Agents"
    ],
    "Marketing": [
      "Content Automation", "Social Media AI", "Email Marketing AI", 
      "SEO Automation", "AI Content Supply Chain", "Personalized Content Engines",
      "Automated Brand Voice Systems", "AI SEO Content Clusters"
    ],
    "Sales": [
      "Sales Automation", "CRM Integration", "Proposal Generation AI", 
      "Follow-up Automation", "AI Sales Agents"
    ],
    "Operations": [
      "Workflow Automation", "Document Processing AI", "Scheduling Automation", 
      "Inventory Management AI", "Custom Operational Dashboards",
      "Subscription Chaos Consolidation", "Custom Internal Software", "API Integration Hubs"
    ],
    "Analytics": [
      "AI Dashboards", "Predictive Analytics", "Business Intelligence AI", 
      "Reporting Automation"
    ],
    "Intelligence & Research": [
      "Competitive Analysis Agents", "Market Trend Monitoring Systems",
      "Automated Due Diligence AI", "Social Media Intelligence Tools"
    ]
  };

  // No-code platforms for comparison titles
  const noCodePlatforms = ["Zapier", "Make.com", "n8n", "ChatGPT Plus"];

  // Enhanced title templates with problem/solution and comparison focus
  const titleTemplates = [
    // Original
    "{qualifier} {service} for {industry}",
    "{qualifier} {service} for {industry} in 2025",
    "How to Choose the {qualifier} {service} for {industry}",
    "{industry}: {qualifier} {service} Comparison",
    "{qualifier} {product} for {industry} Businesses",
    "Why {industry} Companies Need {service}",
    "{industry} AI Solutions: {qualifier} {service}",
    "{qualifier} {service} Specialized for {industry}",
    "Transform Your {industry} Business with {service}",
    "{product} for {industry}: {qualifier} Solutions",
    "{qualifier} Companies Providing {service} for {industry}",
    "Hire {qualifier} {service} for Your {industry} Business",
    "{industry} Automation: {qualifier} {service}",
    "{qualifier} Custom AI for {industry}",
    "{industry} Digital Transformation: {qualifier} {service}",
    
    // Problem/Solution Templates
    "Tired of Broken Workflows? How {service} Can Help Your {industry} Business",
    "Is {platform} Holding Your {industry} Business Back?",
    "Beyond {platform}: {qualifier} {service} for {industry}",
    
    // Comparison Templates
    "{service} vs. {platform}: A Guide for {industry}",
    "Custom AI Agents vs. {platform} for {industry}",
    "{qualifier} {platform} Alternative for {industry}",
    "5 Signs You've Outgrown {platform} in Your {industry} Business",
    "Why {industry} Companies Are Moving from {platform} to Custom AI",
    
    // How-To & Expert Positioning
    "How to Build a {product} System for Your {industry} Company",
    "The Ultimate Guide to {service} for {industry}",
    "Building vs. Buying: {service} for {industry}",
    "{industry} Guide: When to Move Beyond {platform}",
    
    // ROI & Cost Templates
    "The True Cost of {platform} for {industry} Companies",
    "ROI Analysis: Custom {service} vs. {platform} for {industry}",
    "Cost Breakdown: {service} for {industry} Businesses"
  ];

  const generateTitles = () => {
    setIsGenerating(true);
    const titles = new Set();
    
    // Strategy 1: Core Service + Industry combinations
    serviceTypes.forEach(service => {
      industries.forEach(industry => {
        qualifiers.slice(0, 8).forEach(qualifier => {
          titleTemplates.filter(t => !t.includes('{platform}')).slice(0, 4).forEach(template => {
            const title = template
              .replace('{qualifier}', qualifier)
              .replace('{service}', service)
              .replace('{industry}', industry);
            titles.add(title);
          });
        });
      });
    });

    // Strategy 2: Specific Products + Industries
    Object.entries(specificProducts).forEach(([category, products]) => {
      products.forEach(product => {
        industries.forEach(industry => {
          qualifiers.slice(0, 6).forEach(qualifier => {
            const template = titleTemplates.filter(t => !t.includes('{platform}'))[Math.floor(Math.random() * titleTemplates.filter(t => !t.includes('{platform}')).length)];
            const title = template
              .replace('{qualifier}', qualifier)
              .replace('{service}', product)
              .replace('{product}', product)
              .replace('{industry}', industry);
            titles.add(title);
          });
        });
      });
    });

    // Strategy 3: Platform Comparison Titles (HIGH VALUE!)
    noCodePlatforms.forEach(platform => {
      industries.forEach(industry => {
        serviceTypes.slice(0, 15).forEach(service => {
          titleTemplates.filter(t => t.includes('{platform}')).forEach(template => {
            const title = template
              .replace('{platform}', platform)
              .replace('{service}', service)
              .replace('{industry}', industry)
              .replace('{qualifier}', qualifiers[Math.floor(Math.random() * 5)]);
            titles.add(title);
          });
        });
      });
    });

    // Strategy 4: High-intent problem/solution variations
    const painPoints = [
      "Subscription Chaos", "Broken Integrations", "Manual Data Entry",
      "Scaling Issues", "High Per-Seat Costs", "Workflow Bottlenecks"
    ];

    painPoints.forEach(pain => {
      industries.slice(0, 40).forEach(industry => {
        titles.add(`How to Solve ${pain} in ${industry} with Custom AI`);
        titles.add(`${industry} Companies: Stop Paying for ${pain}`);
      });
    });

    // Strategy 5: Product-specific comparison titles
    Object.entries(specificProducts).forEach(([category, products]) => {
      products.slice(0, 3).forEach(product => {
        noCodePlatforms.forEach(platform => {
          industries.slice(0, 30).forEach(industry => {
            titles.add(`${product} vs. ${platform} for ${industry}`);
            titles.add(`Building Custom ${product} for ${industry}: Beyond ${platform}`);
          });
        });
      });
    });

    // Convert to array and limit to target count
    const titlesArray = Array.from(titles).slice(0, targetCount);
    
    // Add metadata
    const titlesWithMeta = titlesArray.map((title, index) => ({
      id: index + 1,
      title: title,
      category: getCategoryFromTitle(title),
      intent: 'BOFU',
      hasComparison: title.includes('vs.') || noCodePlatforms.some(p => title.includes(p)),
      estimatedSearchVolume: Math.floor(Math.random() * 500) + 50
    }));

    setGeneratedTitles(titlesWithMeta);
    setIsGenerating(false);
  };

  const getCategoryFromTitle = (title) => {
    if (title.includes('Lead') || title.includes('SDR')) return 'Lead Generation';
    if (title.includes('Customer Service') || title.includes('Chatbot') || title.includes('Voice AI') || title.includes('Support')) return 'Customer Service';
    if (title.includes('Marketing') || title.includes('Content') || title.includes('Social') || title.includes('SEO')) return 'Marketing';
    if (title.includes('Sales') || title.includes('CRM')) return 'Sales';
    if (title.includes('Workflow') || title.includes('Automation') || title.includes('Operations') || title.includes('Dashboard')) return 'Operations';
    if (title.includes('Intelligence') || title.includes('Research') || title.includes('Analysis')) return 'Intelligence & Research';
    if (title.includes('vs.') || title.includes('Alternative') || title.includes('Outgrown') || noCodePlatforms.some(p => title.includes(p))) return 'Platform Comparison';
    return 'General AI Development';
  };

  const downloadCSV = () => {
    const headers = ['ID', 'Title', 'Category', 'Intent', 'Has Comparison', 'Estimated Volume'];
    const csvContent = [
      headers.join(','),
      ...generatedTitles.map(item => 
        `${item.id},"${item.title}",${item.category},${item.intent},${item.hasComparison},${item.estimatedSearchVolume}`
      )
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `aiq-bofu-titles-${targetCount}-${Date.now()}.csv`;
    a.click();
  };

  const downloadJSON = () => {
    const json = JSON.stringify(generatedTitles, null, 2);
    const blob = new Blob([json], { type: 'application/json' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `aiq-bofu-titles-${targetCount}-${Date.now()}.json`;
    a.click();
  };

  const categoryColors = {
    'Lead Generation': 'bg-green-500/20 text-green-300',
    'Customer Service': 'bg-blue-500/20 text-blue-300',
    'Marketing': 'bg-purple-500/20 text-purple-300',
    'Sales': 'bg-pink-500/20 text-pink-300',
    'Operations': 'bg-orange-500/20 text-orange-300',
    'Intelligence & Research': 'bg-cyan-500/20 text-cyan-300',
    'Platform Comparison': 'bg-red-500/20 text-red-300',
    'General AI Development': 'bg-gray-500/20 text-gray-300'
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 mb-8 border border-white/20">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold text-white mb-2">AIQ Labs BOFU Title Generator</h1>
              <p className="text-purple-200">Generate thousands of high-intent article titles targeting businesses ready to hire custom AI developers</p>
            </div>
            <div className="text-right">
              <div className="text-3xl font-bold text-white">{targetCount.toLocaleString()}</div>
              <div className="text-purple-300 text-sm">Target Titles</div>
            </div>
          </div>
        </div>

        {/* Controls */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 mb-8 border border-white/20">
          <div className="flex items-center gap-4 flex-wrap">
            <div className="flex-1 min-w-64">
              <label className="block text-purple-200 mb-2 text-sm font-medium">
                Target Title Count
              </label>
              <input
                type="number"
                value={targetCount}
                onChange={(e) => setTargetCount(Math.min(50000, Math.max(100, parseInt(e.target.value) || 1000)))}
                className="w-full bg-white/20 border border-white/30 rounded-lg px-4 py-2 text-white placeholder-purple-300"
                min="100"
                max="50000"
                step="100"
              />
            </div>
            
            <button
              onClick={generateTitles}
              disabled={isGenerating}
              className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 disabled:opacity-50 text-white px-8 py-3 rounded-lg font-semibold flex items-center gap-2 transition-all transform hover:scale-105"
            >
              <Shuffle size={20} />
              {isGenerating ? 'Generating...' : 'Generate Titles'}
            </button>

            <button
              onClick={() => setShowSettings(!showSettings)}
              className="bg-white/20 hover:bg-white/30 text-white px-4 py-3 rounded-lg flex items-center gap-2 transition-all"
            >
              <Settings size={20} />
            </button>
          </div>

          {showSettings && (
            <div className="mt-6 pt-6 border-t border-white/20">
              <h3 className="text-white font-semibold mb-4">Generator Configuration</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-6">
                <div className="bg-white/10 rounded-lg p-3">
                  <div className="text-purple-300">Qualifiers</div>
                  <div className="text-2xl font-bold text-white">{qualifiers.length}</div>
                </div>
                <div className="bg-white/10 rounded-lg p-3">
                  <div className="text-purple-300">Services</div>
                  <div className="text-2xl font-bold text-white">{serviceTypes.length}</div>
                </div>
                <div className="bg-white/10 rounded-lg p-3">
                  <div className="text-purple-300">Industries</div>
                  <div className="text-2xl font-bold text-white">{industries.length}</div>
                </div>
                <div className="bg-white/10 rounded-lg p-3">
                  <div className="text-purple-300">Templates</div>
                  <div className="text-2xl font-bold text-white">{titleTemplates.length}</div>
                </div>
              </div>
              
              <div className="bg-purple-500/20 rounded-lg p-4 border border-purple-500/30">
                <h4 className="text-purple-200 font-semibold mb-2">🎯 Enhanced Features:</h4>
                <ul className="text-purple-100 text-sm space-y-1 list-disc list-inside">
                  <li>Platform comparison titles (vs. Zapier, Make.com, n8n, ChatGPT Plus)</li>
                  <li>Pain point & solution-focused qualifiers</li>
                  <li>AI Agent-specific service positioning</li>
                  <li>Digital-first & high-value industry targets</li>
                  <li>ROI and cost analysis title variations</li>
                  <li>Product category: Intelligence & Research</li>
                </ul>
              </div>
            </div>
          )}
        </div>

        {/* Results */}
        {generatedTitles.length > 0 && (
          <>
            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 mb-8 border border-white/20">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h2 className="text-2xl font-bold text-white">Generated Titles</h2>
                  <p className="text-purple-200">{generatedTitles.length.toLocaleString()} titles ready | {generatedTitles.filter(t => t.hasComparison).length} include platform comparisons</p>
                </div>
                <div className="flex gap-3">
                  <button
                    onClick={downloadCSV}
                    className="bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded-lg font-semibold flex items-center gap-2 transition-all"
                  >
                    <Download size={18} />
                    Export CSV
                  </button>
                  <button
                    onClick={downloadJSON}
                    className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg font-semibold flex items-center gap-2 transition-all"
                  >
                    <Download size={18} />
                    Export JSON
                  </button>
                </div>
              </div>

              {/* Category breakdown */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-6">
                {[...new Set(generatedTitles.map(t => t.category))].sort().map(category => (
                  <div key={category} className={`rounded-lg p-3 ${categoryColors[category] || 'bg-white/10 text-white'}`}>
                    <div className="text-xs mb-1 opacity-80">{category}</div>
                    <div className="font-bold text-lg">
                      {generatedTitles.filter(t => t.category === category).length}
                    </div>
                  </div>
                ))}
              </div>

              {/* Sample titles */}
              <div className="bg-white/5 rounded-lg p-4 max-h-96 overflow-y-auto">
                <h3 className="text-white font-semibold mb-3">Sample Titles (first 50)</h3>
                <div className="space-y-2">
                  {generatedTitles.slice(0, 50).map((item) => (
                    <div key={item.id} className="flex items-start gap-3 text-sm bg-white/5 p-3 rounded hover:bg-white/10 transition-colors">
                      <span className="text-purple-400 font-mono text-xs">{item.id}</span>
                      <span className="text-white flex-1">{item.title}</span>
                      <div className="flex gap-2">
                        {item.hasComparison && (
                          <span className="text-xs bg-red-500/20 text-red-300 px-2 py-1 rounded">vs.</span>
                        )}
                        <span className={`text-xs px-2 py-1 rounded ${categoryColors[item.category]}`}>
                          {item.category}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </>
        )}

        {/* Instructions */}
        {generatedTitles.length === 0 && (
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/20">
            <h2 className="text-2xl font-bold text-white mb-4">🎯 What Makes This Generator Special</h2>
            <div className="space-y-4 text-purple-100">
              <p className="text-lg">This isn't just another title generator. It's specifically designed to position AIQ Labs as <strong className="text-white">custom AI builders</strong>, not no-code assemblers.</p>
              
              <div className="grid md:grid-cols-2 gap-6 mt-6">
                <div className="bg-purple-500/20 rounded-lg p-4 border border-purple-500/30">
                  <h3 className="text-white font-semibold mb-3">📊 Data Components</h3>
                  <ul className="space-y-2 text-sm">
                    <li><strong className="text-purple-300">30+ qualifiers</strong> - Including pain-point and ROI focused</li>
                    <li><strong className="text-purple-300">35+ services</strong> - AI Agents, custom development, consolidation</li>
                    <li><strong className="text-purple-300">100+ industries</strong> - From plumbing to private equity</li>
                    <li><strong className="text-purple-300">30+ products</strong> - Across 7 specialized categories</li>
                    <li><strong className="text-purple-300">30+ templates</strong> - Problem/solution & comparison focused</li>
                  </ul>
                </div>
                
                <div className="bg-pink-500/20 rounded-lg p-4 border border-pink-500/30">
                  <h3 className="text-white font-semibold mb-3">🔥 High-Value Title Types</h3>
                  <ul className="space-y-2 text-sm">
                    <li><strong className="text-pink-300">Platform Comparisons</strong> - "vs. Zapier", "vs. Make.com", "vs. n8n"</li>
                    <li><strong className="text-pink-300">Pain Point Targeting</strong> - "Tired of Broken Workflows?"</li>
                    <li><strong className="text-pink-300">ROI & Cost Analysis</strong> - "True Cost of...", "ROI Analysis..."</li>
                    <li><strong className="text-pink-300">Migration Stories</strong> - "Why Companies Are Moving From..."</li>
                    <li><strong className="text-pink-300">Building Guides</strong> - "How to Build Custom..." </li>
                  </ul>
                </div>
              </div>
              
              <div className="bg-white/10 p-4 rounded-lg mt-6">
                <p className="text-purple-200">
                  <strong className="text-white">Result:</strong> Over 100,000+ possible combinations that strategically position AIQ Labs against no-code platforms and highlight your custom development expertise.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default BOFUTitleGenerator;