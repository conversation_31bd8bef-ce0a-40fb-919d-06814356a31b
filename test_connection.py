#!/usr/bin/env python3
"""
Test script to verify LM Studio connection and basic functionality
"""

import requests
import json

# Configuration
API_URL = "http://*************:1234/v1/chat/completions"
MODEL_NAME = "qwen3-4b-instruct-2507"

def test_connection():
    """Test connection to LM Studio API."""
    print("Testing connection to LM Studio...")
    
    headers = {
        "Content-Type": "application/json"
    }
    
    data = {
        "model": MODEL_NAME,
        "messages": [
            {"role": "user", "content": "Hello! Please respond with 'Connection successful' if you can read this."}
        ],
        "temperature": 0.3,
        "max_tokens": 50
    }
    
    try:
        response = requests.post(API_URL, headers=headers, json=data, timeout=30)
        if response.status_code == 200:
            result = response.json()
            content = result['choices'][0]['message']['content']
            print(f"✅ Connection successful!")
            print(f"Model response: {content}")
            return True
        else:
            print(f"❌ Error: API returned status code {response.status_code}")
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error connecting to LLM: {e}")
        return False

def test_json_parsing():
    """Test JSON response parsing."""
    print("\nTesting JSON response parsing...")
    
    headers = {
        "Content-Type": "application/json"
    }
    
    prompt = """Please respond with this exact JSON format:
{
  "parent_category": "AI Legal Solutions & Document Management",
  "child_category": "Contract AI & Legal Document Automation",
  "content_focus": "This is a test response to verify JSON parsing works correctly."
}"""
    
    data = {
        "model": MODEL_NAME,
        "messages": [
            {"role": "user", "content": prompt}
        ],
        "temperature": 0.3,
        "max_tokens": 200
    }
    
    try:
        response = requests.post(API_URL, headers=headers, json=data, timeout=30)
        if response.status_code == 200:
            result = response.json()
            content = result['choices'][0]['message']['content']
            print(f"Raw response: {content}")
            
            # Try to parse JSON
            start = content.find('{')
            end = content.rfind('}') + 1
            if start != -1 and end > start:
                json_str = content[start:end]
                parsed = json.loads(json_str)
                print(f"✅ JSON parsing successful!")
                print(f"Parsed data: {parsed}")
                return True
            else:
                print(f"❌ Could not find JSON in response")
                return False
        else:
            print(f"❌ Error: API returned status code {response.status_code}")
            return False
    except json.JSONDecodeError as e:
        print(f"❌ JSON parsing error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Run all tests."""
    print("=== LM Studio Connection Test ===\n")
    
    # Test basic connection
    connection_ok = test_connection()
    
    if connection_ok:
        # Test JSON parsing
        json_ok = test_json_parsing()
        
        if json_ok:
            print(f"\n✅ All tests passed! Ready to run the categorizer.")
        else:
            print(f"\n⚠️  Connection works but JSON parsing needs attention.")
    else:
        print(f"\n❌ Connection failed. Please check:")
        print(f"   - LM Studio is running on {API_URL}")
        print(f"   - Model '{MODEL_NAME}' is loaded")
        print(f"   - No firewall blocking the connection")

if __name__ == "__main__":
    main()
