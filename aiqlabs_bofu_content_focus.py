#!/usr/bin/env python3
"""
AIQ Labs BOFU Content Focus Generator

Reads an existing categorized CSV with columns:
  - original_question, parent_category, child_category, (content_focus is ignored)
Generates a new, bottom-of-funnel (BOFU) optimized `content_focus` paragraph for each row
using the updated AIQ Labs business context, with heavy SEO considerations.

Output: a new CSV with the same first three columns plus the regenerated `content_focus`.

Notes:
- Uses local LM Studio (OpenAI-compatible) at http://192.168.0.219:1234
- Model: qwen3-4b-instruct-2507
- No fabricated facts or case studies. Keep claims aligned with provided context.
"""

import csv
import json
import time
import os
import argparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, List, Tuple
import requests

# LM Studio configuration (from test_connection.py)
LMSTUDIO_API_URL = "http://192.168.0.219:1234/v1/chat/completions"
LMSTUDIO_MODEL = "qwen3-4b-instruct-2507"
DEFAULT_CONCURRENT = 5


def read_business_context(path: str) -> str:
    with open(path, "r", encoding="utf-8") as f:
        return f.read()


def read_rows_first_three_cols(path: str) -> List[Dict[str, str]]:
    rows: List[Dict[str, str]] = []
    with open(path, "r", encoding="utf-8") as f:
        reader = csv.DictReader(f)
        headers = reader.fieldnames or []
        # Prefer named columns if present; otherwise fallback to first 3 columns by order
        col_q = "original_question" if "original_question" in headers else (headers[0] if len(headers) > 0 else None)
        col_p = "parent_category" if "parent_category" in headers else (headers[1] if len(headers) > 1 else None)
        col_c = "child_category" if "child_category" in headers else (headers[2] if len(headers) > 2 else None)

        for row in reader:
            rows.append({
                "original_question": (row.get(col_q) or "").strip() if col_q else "",
                "parent_category": (row.get(col_p) or "").strip() if col_p else "",
                "child_category": (row.get(col_c) or "").strip() if col_c else "",
            })
    return rows


def build_prompt(question: str, parent: str, child: str, business_context: str) -> str:
    return f"""
You are an expert AI copy strategist for AIQ Labs. Your task is to craft a single, bottom-of-the-funnel (BOFU) content_focus paragraph that sets the conversion-focused angle and tone for a blog article.

BUSINESS CONTEXT (use as the only source of truth; do not invent facts):
{business_context}

TARGET READER: SMB owners and marketing leaders evaluating AI implementation.

INPUT ROW:
- Question: "{question}"
- Parent Category: "{parent}"
- Child Category: "{child}"

CRITICAL REQUIREMENTS:
- BOFU intent: focus on implementation guidance, vendor selection criteria, integration path, timelines, pricing models, ROI framing, objection handling, and next-step CTA (e.g., AI Audit & Strategy, AI Workflow Fix, Department Automation, or Complete Business AI System). Avoid hype.
- SEO: naturally include the primary keyphrase closely matching the question; add 1–2 semantically related terms (synonyms or modifiers). No keyword stuffing.
- Compliance: do NOT fabricate metrics, case studies, endorsements, or comparisons absent from the context. Only use benefits and outcomes described or generic/non-numeric claims.
- Brand alignment: emphasize AIQ Labs’ unified multi-agent systems, ownership model (no subscription chaos), secure integrations, and scalability.
- Length: 3–5 sentences. Professional, specific, and conversion-oriented.

OUTPUT:
Respond with EXACTLY this JSON (no extra text):
{{
  "content_focus": "<one-paragraph BOFU angle complying with all rules>"
}}
"""


def call_lm_studio(prompt: str, max_tokens: int = 220, temperature: float = 0.3) -> str:
    headers = {"Content-Type": "application/json"}
    payload = {
        "model": LMSTUDIO_MODEL,
        "messages": [
            {"role": "user", "content": prompt}
        ],
        "temperature": temperature,
        "max_tokens": max_tokens,
    }
    max_retries = 3
    for attempt in range(max_retries):
        try:
            resp = requests.post(LMSTUDIO_API_URL, headers=headers, json=payload, timeout=60)
            if resp.status_code == 200:
                data = resp.json()
                return data["choices"][0]["message"]["content"]
            else:
                if attempt < max_retries - 1:
                    time.sleep(2)
                    continue
                return f"ERROR: HTTP {resp.status_code}: {resp.text[:200]}"
        except Exception as e:
            if attempt < max_retries - 1:
                time.sleep(3)
                continue
            return f"ERROR: {e}"


def extract_content_focus(text: str) -> str:
    if not text:
        return ""
    try:
        start = text.find('{')
        end = text.rfind('}') + 1
        if start != -1 and end > start:
            json_str = text[start:end]
            obj = json.loads(json_str)
            val = obj.get("content_focus", "")
            return val.strip()
    except Exception:
        pass
    # Fallback: return trimmed text when JSON parsing fails
    return text.strip()


def generate_for_row(idx_row: Tuple[int, Dict[str, str]], business_context: str) -> Tuple[int, Dict[str, str]]:
    idx, row = idx_row
    q = row.get("original_question", "")
    parent = row.get("parent_category", "")
    child = row.get("child_category", "")

    prompt = build_prompt(q, parent, child, business_context)
    raw = call_lm_studio(prompt)
    content_focus = extract_content_focus(raw)

    # Minimal sanitation
    content_focus = content_focus.replace("\n", " ").strip()

    return idx, {
        "original_question": q,
        "parent_category": parent,
        "child_category": child,
        "content_focus": content_focus,
    }


def save_output(rows: List[Dict[str, str]], path: str) -> None:
    fieldnames = ["original_question", "parent_category", "child_category", "content_focus"]
    with open(path, "w", newline="", encoding="utf-8") as f:
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        writer.writeheader()
        for r in rows:
            writer.writerow({k: (r.get(k, "") or "") for k in fieldnames})


def main():
    parser = argparse.ArgumentParser(description="Generate BOFU content_focus for AIQ Labs from existing categorized CSV")
    parser.add_argument("--input", default="agentive_aiq_categorized_questions - agentive_aiq_categorized_questions.csv.csv", help="Input CSV with at least the first three columns present")
    parser.add_argument("--context", default="AIQ Labs Context.txt", help="Business context text file for AIQ Labs")
    parser.add_argument("--output", default="agentive_aiq_categorized_questions_bofu.csv", help="Output CSV path")
    parser.add_argument("--concurrent", type=int, default=DEFAULT_CONCURRENT, help="Concurrent requests (default: 5)")
    parser.add_argument("--test", action="store_true", help="Process only the first 30 rows")

    args = parser.parse_args()

    for p in [args.input, args.context]:
        if not os.path.exists(p):
            print(f"Error: file not found -> {p}")
            return

    print("=== AIQ Labs BOFU Content Focus Generator ===")
    print(f"Input:  {args.input}")
    print(f"Context: {args.context}")
    print(f"Output: {args.output}")

    business_context = read_business_context(args.context)
    rows = read_rows_first_three_cols(args.input)

    if args.test:
        rows = rows[:30]
        print(f"Test mode enabled. Processing {len(rows)} rows...")
    else:
        print(f"Processing {len(rows)} rows with concurrency={args.concurrent}...")

    # Concurrent generation
    start = time.time()
    results_buffer: List[Tuple[int, Dict[str, str]]] = []
    with ThreadPoolExecutor(max_workers=args.concurrent) as ex:
        futures = [ex.submit(generate_for_row, (i, row), business_context) for i, row in enumerate(rows)]
        for i, fut in enumerate(as_completed(futures), 1):
            try:
                results_buffer.append(fut.result())
                if i % 25 == 0:
                    print(f"  [OK] {i}/{len(futures)} completed")
            except Exception as e:
                print(f"  [ERROR] worker failed: {e}")

    # Restore original order
    results_buffer.sort(key=lambda x: x[0])
    final_rows = [r for _, r in results_buffer]

    save_output(final_rows, args.output)

    elapsed = time.time() - start
    print(f"Done. Wrote {len(final_rows)} rows -> {args.output}")
    print(f"Elapsed: {elapsed/60:.1f} min | Avg/row: {elapsed/max(1,len(final_rows)):.2f}s")


if __name__ == "__main__":
    main()

