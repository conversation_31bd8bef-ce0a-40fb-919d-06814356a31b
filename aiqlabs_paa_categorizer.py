#!/usr/bin/env python3
"""
AIQ Labs PAA Question Categorizer

This script categorizes People Also Ask (PAA) questions for AIQ Labs blog content creation.
It focuses on bottom-of-the-funnel content that aligns with AIQ Labs' business context.

Output format: original_question,parent_category,child_category,content_focus
"""

import csv
import json
import argparse
import requests
import time
import os
from typing import List, Dict, Tuple

# Configuration for local LM Studio API
API_URL = "http://*************:1234/v1/chat/completions"
MODEL_NAME = "qwen3-4b-instruct-2507"

def read_paa_questions(filename: str) -> List[str]:
    """Read PAA questions from CSV file."""
    questions = []
    with open(filename, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            # Handle different possible column names
            if 'Questions' in row:
                questions.append(row['Questions'])
            elif 'original_question' in row:
                questions.append(row['original_question'])
            elif 'PAA_Question' in row:
                questions.append(row['PAA_Question'])
            else:
                # Use first column value
                first_column = next(iter(row.values()))
                questions.append(first_column)
    return questions

def read_blog_categories(filename: str) -> List[Tuple[str, str]]:
    """Read blog categories from CSV file."""
    categories = []
    with open(filename, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            parent = row['Parent Category'].strip()
            child = row['Child Category'].strip()
            categories.append((parent, child))
    return categories

def read_business_context(filename: str) -> str:
    """Read business context from text file."""
    with open(filename, 'r', encoding='utf-8') as file:
        return file.read()

def get_categories_context(valid_categories: List[Tuple[str, str]]) -> str:
    """Return formatted category pairs for the AI to choose from."""
    context = "You MUST choose from one of these EXACT parent-child category pairs:\n\n"
    
    for i, (parent, child) in enumerate(valid_categories, 1):
        context += f"{i}. Parent: \"{parent}\" | Child: \"{child}\"\n"
    
    context += "\nYou are REQUIRED to select one of these exact pairs. Do not create new categories or modify the names."
    return context

def query_local_llm(prompt: str, max_tokens: int = 600) -> str:
    """Query the local LLM via LM Studio API."""
    headers = {
        "Content-Type": "application/json"
    }
    
    data = {
        "model": MODEL_NAME,
        "messages": [
            {"role": "user", "content": prompt}
        ],
        "temperature": 0.3,  # Lower temperature for more consistent categorization
        "max_tokens": max_tokens
    }
    
    try:
        response = requests.post(API_URL, headers=headers, json=data, timeout=30)
        if response.status_code == 200:
            result = response.json()
            return result['choices'][0]['message']['content']
        else:
            print(f"Error: API returned status code {response.status_code}")
            return "Error processing request"
    except Exception as e:
        print(f"Error connecting to LLM: {e}")
        return "Error connecting to LLM"

def is_bottom_funnel_question(question: str, business_context: str) -> bool:
    """Determine if a question is bottom-of-the-funnel (implementation/solution focused)."""
    question_lower = question.lower()
    
    # Bottom-funnel indicators
    bottom_funnel_keywords = [
        'how to', 'how can', 'best way to', 'implement', 'setup', 'configure',
        'integrate', 'automate', 'cost', 'pricing', 'roi', 'benefits',
        'solution', 'tool', 'platform', 'software', 'system',
        'comparison', 'vs', 'versus', 'alternative', 'replace',
        'workflow', 'process', 'efficiency', 'productivity'
    ]
    
    # Top-funnel indicators (to avoid)
    top_funnel_keywords = [
        'what is', 'what are', 'definition', 'meaning', 'explain',
        'history of', 'who invented', 'when was', 'father of',
        'types of', 'examples of', 'introduction to'
    ]
    
    # Check for bottom-funnel indicators
    has_bottom_funnel = any(keyword in question_lower for keyword in bottom_funnel_keywords)
    
    # Check for top-funnel indicators
    has_top_funnel = any(keyword in question_lower for keyword in top_funnel_keywords)
    
    # Prefer bottom-funnel questions, but don't completely exclude top-funnel if they're business-relevant
    return has_bottom_funnel or not has_top_funnel

def categorize_question(question: str, valid_categories: List[Tuple[str, str]], business_context: str) -> Dict:
    """Categorize a single PAA question for AIQ Labs blog content."""
    
    # First check if it's a good bottom-funnel question
    if not is_bottom_funnel_question(question, business_context):
        # Still process but note it's not ideal
        pass
    
    prompt = f"""
You are an AI assistant helping AIQ Labs categorize People Also Ask (PAA) questions for blog content creation.

BUSINESS CONTEXT:
{business_context}

CRITICAL INSTRUCTIONS:
You MUST select from these EXACT parent-child category pairs. DO NOT create new categories or modify names.

{get_categories_context(valid_categories)}

FOCUS: We want bottom-of-the-funnel content that helps prospects who are already interested in AI solutions and are looking for implementation guidance, comparisons, or specific solutions.

Question: "{question}"

TASK:
1. Determine if this question is relevant to AIQ Labs' business and suitable for a blog article
2. If relevant, categorize it into ONE of the exact parent-child category pairs listed above
3. If NOT relevant to AIQ Labs' business, set both categories to null
4. Provide a content focus paragraph explaining how this connects to AIQ Labs' solutions

You MUST respond with this EXACT JSON format:
{{
  "parent_category": "Copy exact parent name from list above" OR null if not relevant,
  "child_category": "Copy exact child name from list above" OR null if not relevant,
  "content_focus": "A paragraph explaining how this question connects to AIQ Labs' business context and how it can be used for a blog post. Focus on specific AIQ Labs solutions, benefits, and implementation approaches. If not relevant, state 'Not relevant to AIQ Labs business context.'"
}}

REMEMBER:
- Only use the exact category names from the numbered list above
- Focus on questions that help prospects understand implementation, benefits, or solutions
- If the question is too general or not relevant to AIQ Labs' multi-agent AI systems, use null for both categories
- The content_focus should be a substantial paragraph (3-4 sentences) that clearly connects the question to AIQ Labs' specific capabilities
"""

    response = query_local_llm(prompt, max_tokens=600)
    
    # Try to extract JSON from response
    try:
        # Find the first { and last } to extract JSON
        start = response.find('{')
        end = response.rfind('}') + 1
        if start != -1 and end > start:
            json_str = response[start:end]
            result = json.loads(json_str)
            
            # Validate the response
            parent = result.get("parent_category")
            child = result.get("child_category")
            
            # If both are null, the AI determined it's not relevant
            if parent is None and child is None:
                return result
            
            # If we have categories, validate them against our list
            if parent and child:
                parent = parent.strip()
                child = child.strip()
                valid_set = set(valid_categories)
                if (parent, child) in valid_set:
                    return result
            
            # If categories are invalid, mark as not relevant
            return {
                "parent_category": None,
                "child_category": None,
                "content_focus": "Not relevant to AIQ Labs business context."
            }
        else:
            return {
                "parent_category": None,
                "child_category": None,
                "content_focus": "Could not parse AI response."
            }
    except json.JSONDecodeError:
        return {
            "parent_category": None,
            "child_category": None,
            "content_focus": "JSON parsing error in AI response."
        }

def process_questions(questions: List[str], valid_categories: List[Tuple[str, str]], 
                     business_context: str, test_mode: bool = False) -> List[Dict]:
    """Process a list of questions."""
    results = []
    
    # Limit to first 10 questions in test mode
    if test_mode:
        questions = questions[:10]
        print(f"Running in test mode. Processing first {len(questions)} questions...")
    else:
        print(f"Processing {len(questions)} questions...")
    
    try:
        for i, question in enumerate(questions):
            print(f"Processing question {i+1}/{len(questions)}: {question[:60]}...")
            
            result = categorize_question(question, valid_categories, business_context)
            result["original_question"] = question
            
            results.append(result)
            
            # Print result in test mode for verification
            if test_mode:
                print(f"  Result: {result['parent_category']} -> {result['child_category']}")
                print(f"  Focus: {result['content_focus'][:100]}...")
                print()
            
            # Add a small delay to avoid overwhelming the API
            time.sleep(1)
            
            # Save progress every 50 questions
            if not test_mode and (i + 1) % 50 == 0:
                print(f"Progress: {i + 1}/{len(questions)} questions completed")
    
    except KeyboardInterrupt:
        print(f"\nProcess interrupted by user. Processed {len(results)} questions.")
        return results
    except Exception as e:
        print(f"\nError occurred: {e}")
        print(f"Processed {len(results)} questions before error.")
        return results
    
    return results

def save_results_to_csv(results: List[Dict], filename: str):
    """Save results to CSV in the requested format."""
    fieldnames = ["original_question", "parent_category", "child_category", "content_focus"]
    
    with open(filename, 'w', newline='', encoding='utf-8') as file:
        writer = csv.DictWriter(file, fieldnames=fieldnames)
        writer.writeheader()
        
        for result in results:
            # Convert None values to empty strings for CSV
            csv_row = {}
            for field in fieldnames:
                value = result.get(field)
                csv_row[field] = value if value is not None else ""
            writer.writerow(csv_row)
    
    print(f"Results saved to {filename}")

def main():
    """Main function to run the AIQ Labs PAA categorizer."""
    parser = argparse.ArgumentParser(description="Categorize PAA questions for AIQ Labs blog content")
    parser.add_argument("--test", action="store_true", help="Run in test mode with first 10 questions")
    parser.add_argument("--questions", default="AIQLABS_QUESTIONS - How can AI automate the e-discovery process for complex litigation cases__PAA.csv", 
                       help="Input CSV file with PAA questions")
    parser.add_argument("--categories", default="AIQ Labs Blog Categories - Sheet1.csv", 
                       help="CSV file with blog categories")
    parser.add_argument("--context", default="AIQ Labs Context.txt", 
                       help="Text file with business context")
    parser.add_argument("--output", default="aiqlabs_categorized_questions.csv", 
                       help="Output CSV file")
    
    args = parser.parse_args()
    
    print("=== AIQ Labs PAA Question Categorizer ===\n")
    
    # Check if input files exist
    for file_path in [args.questions, args.categories, args.context]:
        if not os.path.exists(file_path):
            print(f"Error: File '{file_path}' not found!")
            return
    
    try:
        # Read business context
        print("Loading business context...")
        business_context = read_business_context(args.context)
        
        # Read valid categories
        print("Loading blog categories...")
        valid_categories = read_blog_categories(args.categories)
        print(f"Loaded {len(valid_categories)} valid category pairs")
        
        # Read questions
        print("Loading PAA questions...")
        questions = read_paa_questions(args.questions)
        print(f"Loaded {len(questions)} questions to categorize")
        
        # Process questions
        results = process_questions(questions, valid_categories, business_context, args.test)
        
        # Save results
        save_results_to_csv(results, args.output)
        
        # Print summary
        relevant_count = sum(1 for r in results if r.get('parent_category') is not None)
        print(f"\n=== SUMMARY ===")
        print(f"Total questions processed: {len(results)}")
        print(f"Relevant for blog content: {relevant_count} ({relevant_count/len(results)*100:.1f}%)")
        print(f"Not relevant: {len(results) - relevant_count} ({(len(results) - relevant_count)/len(results)*100:.1f}%)")
        print(f"\nOutput saved to: {args.output}")
        
    except Exception as e:
        print(f"Error during processing: {e}")
        return

if __name__ == "__main__":
    main()
